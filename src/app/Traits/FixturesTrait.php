<?php

namespace App\Traits;

use App\Http\Resources\JogosHoje\{FixtureEventResource, FixtureResource, FixtureResourceSmall, FixtureStatisticResource};
use App\Models\JogosHoje\{Fixture, Statistic};
use App\Services\JogosHoje\ImagePathService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Collection as SupportCollection;

trait FixturesTrait
{
    private int $minFixturesLimit = 30;

    private function parseTeamFixtures(Collection|SupportCollection $fixtures, Request $request = null): array
    {
        $output = [];
        $fixtureIdsGroupedByLeague = $this->getLeaguesGrouped($fixtures);

        foreach ($fixtureIdsGroupedByLeague as $leagueFixtures) {
            $events = $fixtures->whereIn('id', $leagueFixtures);

            $league = $events->first()->league;

            $formattedEvents = $events->map(function ($item) use ($request) {
                $this->parseFixtureDates(true, $request, $item);

                return $item;
            });

            $output[] = [
                'name' => $league->name,
                'slug' => $league->slug,
                'is_cup' => $league->is_cup,
                'country_name' => $league->country->name,
                'country_slug' => $league->country->slug,
                'league_id' => $league->id,
                'image_url' => ImagePathService::get($league),
                'fixtures' => FixtureResource::collection($formattedEvents),
            ];
        }

        return $output;
    }

    private function getLeaguesGrouped(Collection|SupportCollection $fixtures): array
    {
        if ($fixtures->isEmpty()) {
            return [];
        }

        $fixtureIdsGroupedByLeague = [];
        $currentLeague = $fixtures->first()->league_id;
        $tempArray = [];

        foreach ($fixtures as $fixture) {
            if ($fixture->league_id != $currentLeague) {
                if (!empty($tempArray)) {
                    $fixtureIdsGroupedByLeague[] = $tempArray;
                }
                $tempArray = [];
                $currentLeague = $fixture->league_id;
            }

            $tempArray[] = $fixture->id;
        }

        if (!empty($tempArray)) {
            $fixtureIdsGroupedByLeague[] = $tempArray;
        }

        return $fixtureIdsGroupedByLeague;
    }

    private function parseFixtures(Collection|SupportCollection $fixtures, ?bool $useResource = true, Request $request = null): array
    {
        return $fixtures
            ->groupBy('league_id')
            ->map(function ($events) use ($useResource, $request) {
                $league = $events->first()->league;

                $formattedEvents = $events->map(function ($item) use ($useResource, $request) {
                    $this->parseFixtureDates($useResource, $request, $item);

                    return $item;
                });

                return [
                    'name' => $league->name,
                    'slug' => $league->slug,
                    'is_cup' => $league->is_cup,
                    'country_name' => $league->country->name,
                    'country_slug' => $league->country->slug,
                    'league_id' => $league->id,
                    'image_url' => ImagePathService::get($league),
                    'fixtures' => $useResource ? FixtureResource::collection($formattedEvents) : $formattedEvents,
                ];
            })
            ->values()
            ->all();
    }

    private function parseEvents(Fixture $fixture, ?array $type = []): array
    {
        $fixtureEvents = $fixture->fixtureEvents
            ->sortBy(fn($fe) => $fe->team_id == $fixture->team_home ? 0 : 1)
            ->groupBy('team_id');

        return $fixtureEvents
            ->map(function ($events) use ($type) {
                $team = $events->first()->team;

                return [
                    'team' => [
                        'id' => $team->id,
                        'name' => $team->name,
                        'avatar' => ImagePathService::get($team),
                    ],
                    'events' => FixtureEventResource::collection(
                        $events
                            ->filter(fn($event) => empty($type) || in_array($event->type, $type))
                            ->sortByDesc([['time', 'desc'], ['time_extra', 'desc']])
                    ),
                ];
            })
            ->values()
            ->toArray();
    }

    private function parseForm(SupportCollection $fixtures, Fixture $fixture): array
    {
        $data = [];

        $data[] = $fixtures->filter(fn($f) => $fixture->team_home === $f->team_id)->map(
            fn($item) =>
            $this->calculateForm($item)
        )->reverse()->values()->all();

        $data[] = $fixtures->filter(fn($f) => $fixture->team_away === $f->team_id)->map(
            fn($item) =>
            $this->calculateForm($item)
        )->reverse()->values()->all();

        return $data;
    }

    private function calculateForm(object $item): string
    {
        if (is_null($item->winner)) {
            return 'D';
        }

        return $item->playing_home ? ($item->winner === 'H' ? 'W' : 'L') : ($item->winner === 'A' ? 'W' : 'L');
    }

    private function parseStatistics(Fixture $fixture): array
    {
        $data = [];

        $statisticsSortBy = $fixture->fixtureStatistics
            ->sortBy(fn($fe) => $fe->team_id === $fixture->team_home ? 0 : 1)
            ->groupBy('team_id');

        foreach ($statisticsSortBy as $statistics) {
            $data[] = FixtureStatisticResource::collection($statistics)[0];
        }

        return $data;
    }

    public function parseStreaks(Collection|SupportCollection $data, Request $request, ?Fixture $fixture = null): array
    {
        return $data->map(function ($result) use ($request, $fixture) {
            return $this->parseStreakResult($result, $request, $fixture);
        })->toArray();
    }

    public function parseStreakResult(Statistic $model, Request $request, ?Fixture $fixture = null): array
    {
        $json = $model->toArray();

        if (!empty($model->statisticDetailOne)) {
            $json['statistic_detail_one']['fixtures_in_streak'] = $this->parseFixtures($model->statisticDetailOne->fixturesInStreak, false, $request);
            if ($fixture) {
                $nextMatch = $fixture->getTeamNextMatch($model->statisticDetailOne->team_id);
                if ($nextMatch) {
                    $nextMatch->date = $this->formatNextMatchDate($nextMatch);
                }
                $json['statistic_detail_one']['next_match'] = $nextMatch;
            }
        }

        if (!empty($model->statisticDetailTwo)) {
            $json['statistic_detail_two']['fixtures_in_streak'] = $this->parseFixtures($model->statisticDetailTwo->fixturesInStreak, false, $request);

            if ($fixture) {
                $nextMatch = $fixture->getTeamNextMatch($model->statisticDetailTwo->team_id);
                if ($nextMatch) {
                    $nextMatch->date = $this->formatNextMatchDate($nextMatch);
                }
                $json['statistic_detail_two']['next_match'] = $nextMatch;
            }
        }
        return $json;
    }

    private function formatNextMatchDate(Fixture $fixture): string
    {
        return Carbon::parse($fixture->date)->format('d/m/Y');
    }

    public function handleFixturesPagination(Collection $collection, int $page): array
    {
        $limits = [];

        $total = 0;
        $i = 0;
        $p = 0;
        $e = 0;

        $ids = [];
        foreach ($collection->groupBy('league_id') as $league_id => $items) {
            $ids = array_merge($ids, $items->map(fn($item) => $item->id)->toArray());

            $num = $items->count();

            $total += $num;

            $i += $num;

            if ($i >= $this->minFixturesLimit) {
                $limits[$p] = $i;

                $i = 0;
                $e = 0;
                $p++;
            } else {
                $e += $num;
            }
        }

        if (empty($limits)) {
            $limits[0] = $i;
        }

        if (array_sum(array_values($limits)) !== $total) {
            $limits[$p] = $e;
        }

        $limit = 0;
        if (sizeof($limits) >= $page) {
            $limit = $limits[$page - 1];
        }

        if (($page - 1) == 0) {
            return [0, $limit, $ids, $collection->count()];
        }

        $offset = $this->calculateOffset($limits, $page);

        return [$offset, $limit, $ids, $collection->count()];
    }

    private function calculateOffset(array $limits, int $page): int
    {
        $count = 0;

        foreach ($limits as $p => $num) {
            if ($p < ($page - 1)) {
                $count += $num;
            }
        }

        return $count;
    }

    public function parseQualificationAndKnockoutGames(Collection $games, ?bool $isKnockout = false): array
    {
        $data = $sorting = [];

        $games = $this->addHashToGames($games);

        $i = 0;
        foreach ($games->groupBy('_round') as $round => $fixtures) {
            if (!isset($data[$round])) {
                $data[$round] = [];
                $sorting[$i] = [];
            }

            foreach ($fixtures->groupBy('hash') as $subFixtures) {
                $matches = $goals = $penaltis = [];
                $total = $subFixtures->count();
                $played = 0;
                foreach ($subFixtures as $fixture) {
                    $played += $this->wasFixturePlayed($fixture);

                    $matches[] = new FixtureResourceSmall($fixture);

                    $this->processFixtureGoalsAndPenaltis($fixture, $goals, $penaltis);
                }
                $data[$round][] = $matches;

                if ($total === $played) {
                    $sorting[$i][] = $this->getFixtureWinnerTeamId($goals, $penaltis);
                }
            }

            $i++;
        }

        if (!$isKnockout) {
            return $data;
        }

        return $this->processKnockoutGamesSorting($games, $sorting);
    }

    private function getFixtureWinnerTeamId(array $goals, array $penaltis): int
    {
        $goalsValues = array_values($goals);
        $goalsWinner = array_search(max($goalsValues), $goalsValues);
        $goalsWinner = $goalsWinner === 0 ? array_key_first($goals) : array_key_last($goals);

        $penaltisValues = array_values($penaltis);
        $penaltisWinner = array_search(max($penaltisValues), $penaltisValues);
        $penaltisWinner = $penaltisWinner === 0 ? array_key_first($penaltis) : array_key_last($penaltis);

        return array_sum($penaltisValues) === 0 ? $goalsWinner : $penaltisWinner;
    }

    private function processFixtureGoalsAndPenaltis(Fixture $fixture, array &$goals, array &$penaltis): void
    {
        if (!isset($goals[$fixture->team_home])) {
            $goals[$fixture->team_home] = 0;
            $goals[$fixture->team_away] = 0;

            $penaltis[$fixture->team_home] = 0;
            $penaltis[$fixture->team_away] = 0;
        }

        $goals[$fixture->team_home] += $fixture->goals_home + (int) $fixture->goals_extra_home;
        $goals[$fixture->team_away] += $fixture->goals_away + (int) $fixture->goals_extra_away;

        $penaltis[$fixture->team_home] += (int) $fixture->penalty_home;
        $penaltis[$fixture->team_away] += (int) $fixture->penalty_away;
    }

    private function wasFixturePlayed(Fixture $fixture): int
    {
        return Fixture::isFinished($fixture->status) ? 1 : 0;
    }


    private function processKnockoutGamesSorting(Collection $games, array &$sorting): array
    {
        $sortedData = [];
        $i = 0;
        foreach ($games->groupBy('_round') as $round => $fixtures) {
            if (!isset($sortedData[$round])) {
                $sortedData[$round] = [];
            }

            $groupedFixtures = $fixtures->groupBy('hash');

            if ($i > 0) {
                $j = 0;
                $groupedFixtures = $groupedFixtures
                    ->sortBy(function ($items, $key) use (&$sorting, $i, $round, $fixtures) {
                        $ids = $items
                            ->pluck('team_home')
                            ->concat($items->pluck('team_away'))
                            ->unique()
                            ->values()
                            ->toArray();

                        $r = !empty($sorting[$i]) ? $i : $i - 1;

                        $k = sizeof($sorting[$r]) - 1;
                        foreach ($sorting[$r] as $idx) {
                            if (in_array($idx, $ids)) {
                                foreach ($ids as $teamId) {
                                    $k = array_search($teamId, $sorting[$i - 1]);
                                    if ($k !== false) {
                                        break;
                                    }
                                }
                            }
                        }

                        return $k;
                    })->each(function ($items, $k) use ($i, &$sorting, &$j) {
                        $goals = $penaltis = [];
                        $total = $items->count();
                        $played = 0;
                        foreach ($items as $item) {
                            $played += $this->wasFixturePlayed($item);

                            $this->processFixtureGoalsAndPenaltis($item, $goals, $penaltis);
                        }

                        if ($total === $played) {
                            $sorting[$i][$j] = $this->getFixtureWinnerTeamId($goals, $penaltis);
                        }
                        $j++;
                    });
            }

            foreach ($groupedFixtures as $subFixtures) {
                $matches = [];
                foreach ($subFixtures as $fixture) {
                    $matches[] = new FixtureResourceSmall($fixture);
                }
                $sortedData[$round][] = $matches;
            }

            $i++;
        }

        return $this->processReverseOrder($sortedData);
    }

    private function processReverseOrder(array &$sortedData): array
    {
        $data = [];
        $sortedDataReversed = array_reverse($sortedData);

        // get first round sorting
        $sorting = $this->getReversedSorting($sortedDataReversed);

        $i = 0;
        foreach ($sortedDataReversed as $round => &$roundFixtures) {
            if ($i == 0) {
                // set data with the first round fixtures (without sorting)
                $data[$round] = $roundFixtures;
            } else {
                // sort round fixtures based on previous round ($sorting[$i - 1])
                $roundFixtures = collect($roundFixtures)
                    ->sortBy(function ($items, $key) use ($sorting, $i) {
                        $ids = $this->getTeamIds($items);

                        $k = false;
                        foreach ($ids as $id) {
                            $k = array_search($id, $sorting[$i - 1]);
                            if ($k !== false) {
                                break;
                            }
                        }

                        return $k === false ? $key : $k;
                    });

                // Update current sorting phase, to be used in next phase
                $ids = [];
                foreach ($roundFixtures as $roundedFixture) {
                    $ids = [...$ids, ...$this->getTeamIds($roundedFixture)];
                }

                $sorting[$i] = $ids;

                // set data with the sorted round fixtures
                $data[$round] = $roundFixtures
                    ->values()
                    ->toArray();
            }

            $i++;
        }

        return array_reverse($data);
    }

    private function getReversedSorting(array $sortedDataReversed): array
    {
        $sorting = [];

        $i = 0;
        foreach ($sortedDataReversed as $fixtures) {
            if (!isset($sorting[$i])) {
                $sorting[$i] = [];
            }

            foreach ($fixtures as $items) {
                $item = collect($items)->first();

                $sorting[$i][] = $item->team_home;
                $sorting[$i][] = $item->team_away;
            }

            $i++;
        }

        return $sorting;
    }

    private function getTeamIds(array $fixtures): array
    {
        $ids = [];
        foreach ($fixtures as $fixture) {
            $ids[] = $fixture->team_home;
            $ids[] = $fixture->team_away;
            break;
        }

        return $ids;
    }

    public function checkIfIsCurrentPhase(?Fixture $fixtureLastRound, array $leagueRounds, ?bool $isStandings = false): ?string
    {
        $round = $fixtureLastRound && in_array($fixtureLastRound->_round, $leagueRounds) ? $fixtureLastRound->_round : null;

        if ($isStandings && $round) {
            $exp = explode(' - ', $round);
            $str = '';

            for ($i = 0; $i < (sizeof($exp) - 1); $i++) {
                $str .= ' - ' . $exp[$i];
            }

            return substr($str, 3);
        }

        return $round;
    }

    private function addHashToGames(Collection $games): Collection
    {
        return $games->map(function ($item) {
            $item->hash = md5(md5($item->team_home) ^ md5($item->team_away));
            return $item;
        });
    }

    public function getDynamicPagination(Collection $collection): array
    {
        $pages = [];
        $total = 0;
        $offset = 0;
        $page = 1;
        $currentPageSize = 0;

        foreach ($collection->groupBy('league_id') as $leagueFixtures) {
            $fixturesCount = $leagueFixtures->count();
            $total += $fixturesCount;
            $currentPageSize += $fixturesCount;

            // If we've reached or exceeded the minimum fixtures limit for a page
            if ($currentPageSize >= $this->minFixturesLimit) {
                // Store the current page with [offset, limit] values
                $pages[$page] = [$offset, $currentPageSize];

                // Update offset for the next page
                $offset += $currentPageSize;

                // Move to next page and reset current page size
                $page++;
                $currentPageSize = 0;
            }
        }

        // Handle any remaining fixtures for the last page
        if ($currentPageSize > 0) {
            $pages[$page] = [$offset, $currentPageSize];
        }

        // If no pages were created (collection was empty), create a default page
        if (empty($pages)) {
            $pages[1] = [0, $currentPageSize];
        }

        return $pages;
    }

    private function parseFixtureDates(?bool $useResource, ?Request $request, &$item): void
    {
        if (!$useResource && $request) {
            $item->full_date = $item->getOriginal('date');
            $item->date = Carbon::parse($item->full_date)
                ->timezone($request->timezone)
                ->format('d/m/Y');
            $item->hour = Carbon::parse($item->full_date)
                ->timezone($request->timezone)
                ->format('H:i');
        }

        if ($item->fixture_id) {
            $item->id = $item->fixture_id;
        }
    }
}
