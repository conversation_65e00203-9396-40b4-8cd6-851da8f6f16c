<?php

namespace App\Models\JogosHoje;

use App\Models\{BlockItem, ContentSeo, Faq, GuiidyModel, MediaSite, Model, Module, Site, SiteModule, User, Version};
use App\Traits\{ConvertsTimezone, CustomRedisTrait};
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\{HasMany, MorphMany};
use Illuminate\Database\Eloquent\{Builder, SoftDeletes};
use Illuminate\Support\Collection as SupportCollection;
use Illuminate\Support\Facades\DB;
use Laravel\Scout\Searchable;
use Watson\Validating\ValidatingTrait;
use App\Traits\ScoutIndexPrefix;

class League extends Model
{
    use HasFactory;
    use ValidatingTrait;
    use GuiidyModel;
    use SoftDeletes;
    use ConvertsTimezone;
    use CustomRedisTrait;
    use Searchable;
    use ScoutIndexPrefix;

    public $timestamps = false;

    protected $fillable = [
        'api_id',
        'name',
        'slug',
        'country_id',
        'is_cup',
        'site_id',
        'media_site_id',
        'created_at',
        'updated_at',
        'modified_at',
        'scheduled_at',
        'published_at',
        'is_published',
        'is_scheduled',
        'update_sync_to_live',
        'h1',
        'changes',
        'version_id',
        'live_version_id',
        'api_name',
        'is_favorite',
        '_order',
        'title',
        'description',
        'is_main',
        'in_use',
        'the_sports_id',
        'main_order',
        'fixtures_order',
        'migrated',
        'has_placeholder',
        'has_groups',
        'has_stats',
        'has_standings',
        'has_players',
        'popularity',
    ];

    protected $casts = [
        'is_cup' => 'boolean',
        'is_published' => 'boolean',
        'is_scheduled' => 'boolean',
        'update_sync_to_live' => 'boolean',
        'is_favorite' => 'boolean',
        'is_main' => 'boolean',
        'in_use' => 'boolean',
        'has_groups' => 'boolean',
        'has_stats' => 'boolean',
        'has_standings' => 'boolean',
        'has_players' => 'boolean',
    ];

    protected ?array $rules = ['api_id' => 'required', 'name' => 'required'];

    public function is_cup()
    {
        return $this->where('is_cup', true);
    }

    public function country()
    {
        return $this->belongsTo(Country::class);
    }

    public function seasons()
    {
        return $this
            ->hasManyThrough(Season::class, LeagueSeason::class, 'league_id', 'id', 'id', 'season_id')
            ->select([
                'seasons.*',
                DB::raw('YEAR(start) start_year'),
                DB::raw('YEAR(end) end_year'),
            ])
            ->orderBy('seasons.season', 'desc');
    }

    public function teams()
    {
        return $this
            ->belongsToMany(Team::class, 'league_teams', 'league_id', 'team_id')
            ->whereNull('teams.deleted_at');
    }

    public function standings(): HasMany
    {
        return $this->hasMany(Standing::class)
            ->join('league_seasons', function ($join) {
                $join->on("league_seasons.season_id", '=', "standings.season_id")
                    ->where("league_seasons.current", DB::raw("1"))
                    ->where("league_seasons.league_id", '=', $this->id);
            })
            ->join(DB::raw("(
                    SELECT
                        ls.season_id
                    FROM league_teams lt
                    JOIN league_seasons ls ON ls.league_id = lt.league_id AND ls.current = 1
                    WHERE
                        lt.league_id = " . $this->id . " AND
                        lt.season_id = ls.season_id
                    GROUP BY ls.season_id
                    ORDER BY ls.season_id DESC
                    LIMIT 1
                ) max_ls"), function ($join) {
                $join->on('max_ls.season_id', '=', 'league_seasons.season_id');
            })
            ->whereRaw("standings.league_id = " . $this->id)
            ->whereRaw("standings.season_id = max_ls.season_id");
    }

    public function players(): HasMany
    {
        return $this->hasMany(TeamPlayer::class)
            ->join('league_seasons', function ($join) {
                $join->on('league_seasons.league_id', '=', 'team_players.league_id');
                $join->on('league_seasons.current', '=', DB::raw("1"));
            })
            ->join(DB::raw("(
                    SELECT
                        ls.season_id
                    FROM league_teams lt
                    JOIN league_seasons ls ON ls.league_id = lt.league_id AND ls.current = 1
                    WHERE
                        lt.league_id = " . $this->id . " AND
                        lt.season_id = ls.season_id
                    GROUP BY ls.season_id
                    ORDER BY ls.season_id DESC
                    LIMIT 1
                ) max_ls"), function ($join) {
                $join->on('max_ls.season_id', '=', 'league_seasons.season_id');
            })
            ->whereRaw("team_players.league_id = " . $this->id)
            ->whereRaw("team_players.season_id = max_ls.season_id");
    }

    public function fixturesCurrentSeason(): HasMany
    {
        return $this->hasMany(Fixture::class)
            ->join('league_seasons', function ($join) {
                $join->on('league_seasons.league_id', '=', 'fixtures.league_id');
                $join->on('league_seasons.current', '=', DB::raw("1"));
            })
            ->join(DB::raw("(
                    SELECT
                        ls.season_id
                    FROM league_teams lt
                    JOIN league_seasons ls ON ls.league_id = lt.league_id AND ls.current = 1
                    WHERE
                        lt.league_id = " . $this->id . " AND
                        lt.season_id = ls.season_id
                    GROUP BY ls.season_id
                    ORDER BY ls.season_id DESC
                    LIMIT 1
                ) max_ls"), function ($join) {
                $join->on('max_ls.season_id', '=', 'league_seasons.season_id');
            })
            ->whereRaw("fixtures.season_id = max_ls.season_id")
            ->where("fixtures.league_id", '=', $this->id);
    }

    public function faqs()
    {
        return $this->hasMany(Faq::class)->where('is_active', true);
    }

    public function blocks()
    {
        return $this->hasMany(BlockItem::class);
    }

    public function fixturesByDate($status = null, $teamId = null, $round = null)
    {
        $selfId = (int) $this->attributes['id'];
        $perPage = (int) request()->query('limit') ? (int) request()->query('limit') : 10;
        $round = request()->query('round') ? request()->query('round') : null;

        $timezone = request()->timezone ?? config('app.timezone');
        $currentDate = Carbon::parse(Carbon::today(), $timezone)->format('Y-m-d H:i:s');

        $season = LeagueSeason::query()
            ->where('current', true)
            ->where('league_id', $selfId)
            ->first();

        switch ($status) {
            case 'future':
                if (!empty($teamId)) {
                    $res = Fixture::whereDate('date', '>=', $currentDate)
                        ->where('league_id', $selfId)
                        ->where(function ($query) use (&$teamId) {
                            $query
                                ->where('team_home', $teamId)
                                ->orWhere('team_away', $teamId);
                        })
                        ->orderBy('date', 'asc')
                        ->orderBy('id', 'asc')
                        ->paginate($perPage);
                } else {
                    $res = Fixture::whereDate('date', '>=', $currentDate)
                        ->where('league_id', $selfId)
                        ->orderBy('date', 'asc')
                        ->orderBy('id', 'asc')
                        ->paginate($perPage);
                }
                break;
            case 'finished':
                if (!empty($teamId)) {
                    $res = Fixture::whereDate('date', '<', $currentDate)
                        ->where('league_id', $selfId)
                        ->where(function ($query) use (&$teamId) {
                            $query
                                ->where('team_home', $teamId)
                                ->orWhere('team_away', $teamId);
                        })
                        ->orderBy('date', 'desc')
                        ->orderBy('id', 'desc')
                        ->paginate($perPage);
                } else {
                    $res = Fixture::whereDate('date', '<', $currentDate)
                        ->where('league_id', $selfId)
                        ->orderBy('date', 'desc')
                        ->orderBy('id', 'desc')
                        ->paginate($perPage);
                }
                break;
            case 'round':
                if (empty($round)) {
                    // get current round
                    $round = Fixture::where('league_id', $selfId)
                        ->whereBetween('date', [now()->subDays(6), now()->addDays(6)])
                        ->orderByRaw("CAST(SUBSTRING_INDEX(round, ' - ', -1) AS UNSIGNED)")
                        ->orderBy('id', 'asc')
                        ->value('round');
                }

                $res = Fixture::where('league_id', $selfId)
                    ->where('round', $round)
                    ->where('date', '>=', date('Y-m-d', strtotime('-2 day', strtotime($season->start))))
                    ->orderBy('date', 'asc')
                    ->orderBy('id', 'asc')
                    ->paginate($perPage);
                break;
            default:
                $res = Fixture::whereDate('date', $status ?? $currentDate)
                    ->where('league_id', $selfId)
                    ->orderBy('date', 'asc')
                    ->orderBy('id', 'asc')
                    ->paginate($perPage);
                break;
        }

        $pagination = [
            'limit' => $res->perPage(),
            'page' => $res->currentPage(),
            'total' => $res->total(),
        ];

        $items = [];
        // prepare fixture data (unset unused fields)
        foreach ($res->items() as $item) {
            unset($item['round']);
            unset($item['api_id']);
            unset($item['deleted_at']);
            unset($item['last_updated']);

            $items[] = $item;
        }

        $items = $this->convertFixturesTimezone($items);

        return [
            'round_name' => $round,
            'items' => $items,
            'pagination' => $pagination,
        ];
    }

    public function getLeagueRounds()
    {
        $selfId = (int) $this->attributes['id'];

        return Fixture::select('round', 'api_id')
            ->where('league_id', $selfId) // TODO: maybe we need to include the current season_id here
            ->orderBy('api_id')
            ->groupBy('api_id', 'round')
            ->get()
            ->unique('round')
            ->pluck('round');
    }

    public function fixtures()
    {
        return $this->hasMany(Fixture::class);
    }

    public function Site()
    {
        return $this->belongsTo(Site::class);
    }

    public function Logo()
    {
        return $this->belongsTo(MediaSite::class, 'media_site_id')->with(['Media']);
    }

    public function hasLive()
    {
        return $this->fixtures()->where('is_live', true);
    }

    public static function getCurrentSeason($league_id)
    {
        return LeagueSeason::getQuery()
            ->selectRaw('(SELECT season FROM seasons WHERE seasons.id = league_seasons.season_id) as season')
            ->where('league_id', $league_id)
            ->where('current', true)
            ->pluck('season');
    }

    public static function boot()
    {
        parent::boot();

        static::created(function ($model) {
            DB::transaction(function () use ($model) {
                $siteId = Site::whereUrl('jogoshoje.com')->first()->id;
                $model->site_id = $siteId;
                $model->save();

                $is_followable = 1;
                $is_indexable = 1;
                if ($module = Module::where('name', 'Leagues')->first()) {
                    if (
                        $siteModule = SiteModule::where('site_id', $model->attributes['site_id'])->where(
                            'module_id',
                            $module->id
                        )->first()
                    ) {
                        $is_followable = $siteModule->default_child_followable;
                        $is_indexable = $siteModule->default_child_indexable;
                    }
                }
                ContentSeo::create([
                    'league_id' => $model->attributes['id'],
                    'site_id' => $model->attributes['site_id'],
                    'title' => 'Tabela ' . $model->attributes['name'] . ' - Jogos e Classificação',
                    'og_title' => 'Tabela ' . $model->attributes['name'] . ' - Jogos e Classificação',
                    'description' => 'Confira os jogos da temporada da ' . $model->attributes['name'] . ', tabela de classificação, resultados, próximos jogos e acompanhe o placar ao vivo.',
                    'og_description' => 'Confira os jogos da temporada da ' . $model->attributes['name'] . ', tabela de classificação, resultados, próximos jogos e acompanhe o placar ao vivo.',
                    // 'og_image_id'    => $model->attributes['media_site_id'], # We don't have an image in our bucket during league creation
                    'canonical_url' => $model->getUrlAttribute(),
                    'is_followable' => $is_followable,
                    'is_indexable' => $is_indexable
                ]);

                $version = Version::firstOrNew(
                    ['site_id' => $model->attributes['site_id'], 'league_id' => $model->attributes['id']]
                );
                $version->description = 'Initial version';
                $version->save();

                League::query()
                    ->where('id', $model->attributes['id'])
                    ->update([
                        'version_id' => $version->id
                    ]);

                return true;
            });
        });

        static::saved(function ($model) {
            if ($seo = ContentSeo::query()->where('league_id', $model->attributes['id'])->first()) {
                $seo->fillBasics($model);
            }
        });
    }

    public function findByApiId($apiId): ?Builder
    {
        if (empty($apiId)) {
            return null;
        }

        return self::where('api_id', $apiId);
    }

    public function persistLeague(array $data): void
    {
        $model = self::firstOrNew(['api_id' => $data['create']['api_id']]);
        $model->fill($model->exists ? $data['update'] : $data['create']);
        $model->save();
    }

    public function lowerDivisions(): Builder
    {
        return self::where('is_cup', 0)
            ->where('in_use', 1)
            ->where('id', '<>', $this->id)
            ->whereRaw(
                "country_id IN (SELECT c.id FROM leagues l JOIN countries c ON c.id = l.country_id WHERE l.id = {$this->id})"
            )
            ->orderBy('api_id', 'asc')
            ->limit(1)
            ->offset(1);
    }

    public function findBySlug($countrySlug, $leagueSlug): ?Builder
    {
        return self::where('slug', $leagueSlug)
            ->whereHas('country', function ($query) use ($countrySlug) {
                $query->where('slug', $countrySlug);
            });
    }

    public function getByApiName($name): ?League
    {
        return self::where('api_name', $name)->first();
    }

    public function getStats(int $leagueId, ?int $seasonId = null): SupportCollection
    {
        $query = "
            SELECT
                COALESCE(SUM(total_games),0) total_games,
                COALESCE(SUM(total_games_played),0) total_games_played,
                ROUND(COALESCE(((SUM(total_games_won_home) / SUM(total_games_played)) * 100), 0), 1) total_games_won_home_percent,
                ROUND(COALESCE(((SUM(total_games_draw_home) / SUM(total_games_played)) * 100), 0), 1) total_games_draw_home_percent,
                ROUND(COALESCE(((SUM(total_games_lost_home) / SUM(total_games_played)) * 100), 0), 1) total_games_lost_home_percent,
                ROUND(COALESCE((SUM(goals_scored_by_match) / SUM(total_games_played)), 0), 1) average_goals_scored_by_match,
                ROUND(COALESCE(((SUM(games_with_more_than_1_5_goals) / SUM(total_games_played)) * 100), 0), 1) games_with_more_than_1_5_goals_percent,
                ROUND(COALESCE(((SUM(games_with_more_than_2_5_goals) / SUM(total_games_played)) * 100), 0), 1) games_with_more_than_2_5_goals_percent,
                ROUND(COALESCE(((SUM(games_with_more_than_3_5_goals) / SUM(total_games_played)) * 100), 0), 1) games_with_more_than_3_5_goals_percent,
                ROUND(COALESCE(((SUM(goals_scored_after_80_minutes) / SUM(goals_scored_by_match)) * 100), 0), 1) goals_scored_after_80_minutes_percent,
                ROUND(COALESCE(((SUM(both_teams_to_score) / SUM(total_games_played)) * 100), 0), 1) both_teams_to_score_percent,
                ROUND(COALESCE(((SUM(clean_sheets) / SUM(total_games_played)) * 100), 0), 1) clean_sheets_percent,
                ROUND(COALESCE((SUM(total_cards) / SUM(total_games_played)), 0), 1) average_cards_by_match,
                ROUND(COALESCE((SUM(total_corners) / SUM(total_games_played)), 0), 1) average_corners_by_match
            FROM (
                SELECT
                    COUNT(distinct f.id) as total_games,
                    IF(f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO'), 1, 0) total_games_played,
                    IF(f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND f.winner IS NOT NULL AND f.winner = 'H', 1, 0) total_games_won_home,
                    IF(f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND f.winner IS NULL, 1, 0) total_games_draw_home,
                    IF(f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND f.winner IS NOT NULL AND f.winner = 'A', 1, 0) total_games_lost_home,
                    (f.goals_home + f.goals_away) goals_scored_by_match,
                    IF((f.goals_home + f.goals_away) > 1.5, 1, 0) games_with_more_than_1_5_goals,
                    IF((f.goals_home + f.goals_away) > 2.5, 1, 0) games_with_more_than_2_5_goals,
                    IF((f.goals_home + f.goals_away) > 3.5, 1, 0) games_with_more_than_3_5_goals,
                    ROUND((SUM(IF(fe.time >= 80 AND fe.type = 'goal' AND fe.detail <> 'missed penalty', 1, 0)) / 2),0) goals_scored_after_80_minutes,
                    IF(f.goals_home > 0 AND f.goals_away > 0, 1, 0) both_teams_to_score,
                    IF(f.goals_home = 0 OR f.goals_away = 0, 1, 0) clean_sheets,
                    (f.cards_home + f.cards_away) total_cards,
                    (f.corners_home + f.corners_away) total_corners
                FROM fixtures f
                LEFT JOIN fixture_events fe ON f.id = fe.fixture_id
                JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
                where
                    f.season_id = " . ($seasonId ?? 'ls.season_id') . " AND
                    f.league_id = ? AND
                    f.deleted_at IS NULL
                GROUP BY f.id
            ) x;
        ";

        return collect(
            DB::selectOne(
                $query,
                [
                    $leagueId,
                ]
            )
        );
    }

    public function favorites(): MorphMany
    {
        return $this->morphMany(User::class, 'favoritable');
    }

    public function getInUseLeagues(): Builder
    {
        return $this->where('in_use', 1);
    }

    public function searchableAs(): string
    {
        return $this->getIndexPrefixByEnvironment() . 'leagues';
    }

    public function shouldBeSearchable(): bool
    {
        return $this->in_use;
    }

    public function toSearchableArray(): array
    {
        return [
            'name' => $this->api_name,
            'slug' => $this->slug,
            'country_id' => $this->country_id,
            'popularity' => $this->popularity,
        ];
    }

    public function increasePopularity(): void
    {
        $this->increment('popularity');
    }
}
