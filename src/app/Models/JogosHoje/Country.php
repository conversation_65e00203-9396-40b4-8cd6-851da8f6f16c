<?php

namespace App\Models\JogosHoje;

use \Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use App\Models\{GuiidyModel, MediaSite, Site};
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\{Model, SoftDeletes};
use Watson\Validating\ValidatingTrait;

class Country extends Model
{
	use HasFactory;
	use ValidatingTrait;
	use GuiidyModel;
	use SoftDeletes;

	public $timestamps = false;

	protected $fillable = [
		'api_name',
		'name',
		'slug',
		'primary_league_id',
		'code',
		'code_3',
		'site_id',
		'media_site_id',
		'created_at',
		'updated_at',
		'is_favorite',
		'_order',
		'api_id'
	];

	protected ?array $rules = [
		'api_name' => 'required',
	];

	protected $casts = [
		'is_favorite' => 'boolean'
	];

	public function primaryLeague()
	{
		return $this->belongsTo(League::class);
	}

	public function getCountryIdByName($item)
	{
		$countryName = null;

		# case Leagues
		if (isset($item->country->name)) {
			$countryName = $item->country->name;
		} elseif (isset($item->team->country)) {
			#case Teams
			$countryName = $item->team->country;
		} elseif (isset($item->country)) {
			#case Player
			$countryName = $item->country;
		}
		else {
			$countryName = $item;
		}

		if ($countryName && !is_object($countryName)) {
			$country = static::where('api_name', $countryName)->first();
			if ($country) {
				return $country->id;
			}
		}

		return null;
	}

	public static function getCountryIdBySlug($slug)
	{
		return Country::where('slug', '=', $slug)->firstOrFail()->toArray();
	}

	public function Site()
	{
		return $this->belongsTo(Site::class);
	}

	public function Flag()
	{
		return $this->belongsTo(MediaSite::class, 'media_site_id')->with(['Media']);
	}

	public function getByApiName($name): ?Country
	{
		return self::where('api_name', $name)->first();
	}

	public static function getByCode(array $data): ?Country
	{
		return self::where(function($q) use ($data){
			if (isset($data['countryIsoCode3'])) {
				$q->where('code_3', $data['countryIsoCode3']);
			}

			if ($data['countryIsoCode2'] && !isset($data['countryIsoCode3'])) {
				$q->where('code', $data['countryIsoCode2']);
			}
		})->first();
	}

	public function persistCountry(array $data): void
	{
		$model = self::firstOrNew(['api_name' => $data['create']['api_name']]);
		$model->fill($model->exists ? $data['update'] : $data['create']);
		$model->save();
	}

	public function leagues(): HasMany
	{
		return $this
			->hasMany(League::class)
			->orderBy('_order');
	}

	public function favoriteLeagues(): HasMany
	{
		return $this
			->hasMany(League::class)
			->where('leagues.is_main', 1)
			->orderBy('leagues._order');
	}

	public function mainLeagues(): HasMany
	{
		return $this
			->hasMany(League::class)
			->where('leagues.is_main', 1)
			->orderBy('leagues._order');
	}

	public function favourites(): EloquentBuilder
	{
		return $this
			->with(['favoriteLeagues'])
			->where('is_favorite', true)
			->where('visible', true)
			->orderBy('_order');
	}
}
