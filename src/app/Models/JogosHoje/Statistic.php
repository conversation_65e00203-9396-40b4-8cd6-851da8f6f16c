<?php

namespace App\Models\JogosHoje;

use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection as SupportCollection;

class Statistic extends Model
{
    use HasFactory;

    protected $fillable = [
        'market_id',
        'score',
        'pretty_name',
        'type',
        'statistic_details_1_id',
        'statistic_details_2_id',
        'fixture_id',
    ];

    public function market(): BelongsTo
    {
        return $this->belongsTo(Market::class);
    }

    public function statisticDetailOne(): BelongsTo
    {
        return $this->belongsTo(StatisticDetails::class, 'statistic_details_1_id')->with(['fixturesInStreak'])->whereNotIn('config_type_id', ['no_clean_sheets', 'scored', 'clean_sheets', 'scoreless']);
    }

    public function statisticDetailTwo(): BelongsTo
    {
        return $this->belongsTo(StatisticDetails::class, 'statistic_details_2_id')->with(['fixturesInStreak'])->whereNotIn('config_type_id', ['no_clean_sheets', 'scored', 'clean_sheets', 'scoreless']);
    }

    public function fixture(): BelongsTo
    {
        return $this->belongsTo(Fixture::class);
    }

    public function streaksTypes(): Builder
    {
        return self::join('markets', 'markets.id', '=', 'statistics.market_id')
            ->join('statistic_details as sd1', 'sd1.id', '=', 'statistics.statistic_details_1_id')
            ->leftJoin('fixtures', 'fixtures.id', '=', 'statistics.fixture_id')
            ->select([
                'markets.type',
                DB::raw("config_type_id name"),
                DB::raw("GROUP_CONCAT(distinct CONCAT(sd1.config_type_id, IF(sd1.config_threshold, CONCAT(' ', sd1.config_threshold), ''))) thresholds")
            ])
            ->whereRaw('fixtures.date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 14 DAY)')
            ->where(function ($qx) {
                $qx->where(function ($q) {
                    $q->where(function ($q1) {
                        $q1->whereNull('sd1.config_threshold')
                            ->whereIn('markets.id', [1, 4]);
                    });
                })
                    ->orWhere(function ($q2) {
                        $q2->whereNotNull('sd1.config_threshold')
                            ->where(function ($q3) {
                                $q3->where(function ($q12) {
                                    $q12->whereIn('sd1.config_threshold', [1.50, 2.50, 3.50])
                                        ->whereIn('markets.id', [4]);
                                })
                                    ->orWhere(function ($q22) {
                                        $q22->whereIn('markets.id', [2, 3])
                                            ->whereIn('sd1.config_threshold', [1.50, 2.50, 3.50, 4.50, 5.50, 6.50, 10.50, 11.50, 12.50, 13.50]);
                                    });
                            });
                    });
            })
            ->groupByRaw("CONCAT(markets.type, REPLACE(REPLACE(config_type_id, '_over', ''), '_under', ''))");
    }

    public function streaks(?int $limit = 20, int $offset = 0): Builder
    {
        $query = self::with(['market'])
            ->join('statistic_details as sd1', 'sd1.id', '=', 'statistics.statistic_details_1_id')
            ->join('teams', 'teams.id', '=', 'sd1.team_id')
            ->leftJoin('fixtures', 'fixtures.id', '=', 'statistics.fixture_id')
            ->leftJoin('teams as t1', 'fixtures.team_home', '=', 't1.id')
            ->leftJoin('teams as t2', 'fixtures.team_away', '=', 't2.id')
            ->whereRaw('fixtures.date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 14 DAY)')
            ->where(function ($q) {
                $q->where(function ($qx) {
                    $qx->where(function ($q1) {
                        $q1->whereNull('sd1.config_threshold')
                            ->whereIn('statistics.market_id', [1, 4]);
                    });
                })
                    ->orWhere(function ($q2) {
                        $q2->whereNotNull('sd1.config_threshold')
                            ->where(function ($q3) {
                                $q3->where(function ($q12) {
                                    $q12->whereIn('sd1.config_threshold', [1.50, 2.50, 3.50])
                                        ->whereIn('statistics.market_id', [4]);
                                })
                                    ->orWhere(function ($q22) {
                                        $q22->whereIn('statistics.market_id', [2, 3])
                                            ->whereIn('sd1.config_threshold', [1.50, 2.50, 3.50, 4.50, 5.50, 6.50, 10.50, 11.50, 12.50, 13.50]);
                                    });
                            });
                    });
            })
            ->select([
                DB::raw('statistics.id'),
                DB::raw('sd1.config_pretty_name as pretty_name'),
                DB::raw('sd1.value as count'),
                DB::raw('sd1.count as games'),
                'sd1.config_type_id',
                'sd1.config_threshold',
                DB::raw('teams.name as team_name'),
                'teams.media_site_id',
                DB::raw('fixtures.id as fixture_id'),
                DB::raw('fixtures.date as fixture_date'),
                'statistics.score',
                DB::raw('((sd1.value / sd1.count) * 100) percentage'),
                'statistics.market_id',
                DB::raw('t1.slug as team_home_slug'),
                DB::raw('t2.slug as team_away_slug'),
            ])
            ->groupBy(DB::raw("CONCAT(teams.id, sd1.config_type_id, fixtures.id)"))
            ->orderByRaw('(sd1.value / sd1.count) DESC')
            ->orderBy('statistics.score', 'DESC')
            ->orderBy('sd1.count', 'DESC');

        if ($limit) {
            $query->limit($limit)->offset($offset);
        }
        return $query;
    }

    public function streaksForATeam(int $teamId, array $types, ?Fixture $fixture = null): Collection|SupportCollection
    {
        if (!$fixture) {
            return collect([]);
        }

        return self::with(['statisticDetailOne', 'fixture'])
            ->whereHas('statisticDetailOne', function ($query) use ($types, $teamId) {
                $query
                    ->whereIn('config_type_id', $types)
                    ->where('team_id', $teamId);
            })
            ->whereHas('fixture', function ($q) use ($fixture) {
                $q->where('fixtures.id', $fixture->id);
            })
            ->get();
    }
}
