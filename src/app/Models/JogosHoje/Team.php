<?php

namespace App\Models\JogosHoje;

use \Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\{BlockItem, ContentSeo, Faq, GuiidyModel, MediaSite, Model, Module, Site, SiteModule, User, Version};
use App\Services\JogosHoje\ImagePathService;
use App\Traits\{ConvertsTimezone, CustomRedisTrait};
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\{HasMany, HasOne, MorphMany};
use Illuminate\Database\Eloquent\{Builder, SoftDeletes};
use Illuminate\Support\Facades\DB;
use Laravel\Scout\Searchable;
use Watson\Validating\ValidatingTrait;
use App\Traits\ScoutIndexPrefix;

class Team extends Model
{
    use HasFactory;
    use ValidatingTrait;
    use GuiidyModel;
    use SoftDeletes;
    use ConvertsTimezone;
    use CustomRedisTrait;
    use Searchable;
    use ScoutIndexPrefix;

    public $timestamps = false;

    protected $fillable = [
        'api_id',
        'name',
        'slug',
        'country_id',
        'national',
        'site_id',
        'media_site_id',
        'created_at',
        'updated_at',
        'squads_at',
        'modified_at',
        'scheduled_at',
        'published_at',
        'is_published',
        'is_scheduled',
        'update_sync_to_live',
        'h1',
        'changes',
        'version_id',
        'live_version_id',
        'is_favorite',
        '_order',
        'api_name',
        'title',
        'description',
        'venue_id',
        'the_sports_id',
        'has_placeholder',
        'popularity',
    ];

    protected $casts = [
        'national' => 'boolean',
        'is_published' => 'boolean',
        'is_scheduled' => 'boolean',
        'is_favorite' => 'boolean',
        'update_sync_to_live' => 'boolean'
    ];

    protected ?array $rules = ['api_id' => 'required', 'name' => 'required'];

    protected $appends = ['h1', 'url', 'is_followable', 'is_indexable'];

    public function BlockItems(): HasMany
    {
        return $this->hasMany(BlockItem::class);
    }

    public function getH1Attribute()
    {
        return !empty($this->attributes['h1']) ? $this->attributes['h1'] : '';
    }

    public static function findBySlug($countrySlug, $teamSlug)
    {
        $TM = 'teams';
        $LG = 'leagues';
        $CT = 'countries';
        $SELECT = ["$TM.id", "$TM.api_id", "$TM.name", "$TM.slug", "$TM.national", "$TM.country_id", "$TM.site_id", "$TM.media_site_id"];
        $SELECT = [
            ...$SELECT,
            ...[
                "$CT.code as country_code",
                "$CT.name as country_name",
                "$CT.slug as country_slug",
            ]
        ];

        $team = Team::with(['faqs', 'seo', 'blocks'])
            ->join($CT, "$CT.id", "$TM.country_id")
            ->where("$TM.slug", $teamSlug)
            ->where("$CT.slug", $countrySlug)
            ->first($SELECT);

        if (empty($team)) {
            return null;
        }

        $country_id = $team->country_id;

        $result = [
            'team' => $team,
            'leagues' => League::select("$LG.name", "$LG.slug")
                ->where("$LG.country_id", '=', $country_id)->get()->toArray()
        ];

        return $result;
    }

    public function teamCurrentLeagues($teamId = null)
    {
        $teamId = $this->id ? $this->id : $teamId;

        return League::whereHas('teams', function ($query) use (&$teamId) {
            $query->where('team_id', $teamId);
        })->whereHas('seasons', function ($query) {
            $query->where('current', true);
        })->get();
    }

    public function leagues()
    {
        return $this
            ->belongsToMany(League::class, 'league_teams', 'team_id', 'league_id')
            ->whereNull('leagues.deleted_at');
    }

    public function country()
    {
        return $this->belongsTo(Country::class);
    }

    public function faqs()
    {
        return $this->hasMany(Faq::class)->where('is_active', true);
    }

    public function logo()
    {
        return $this->belongsTo(MediaSite::class, 'media_site_id')->with(['Media']);
    }

    public function Site()
    {
        return $this->belongsTo(Site::class);
    }

    public function Version()
    {
        return $this->belongsTo(Version::class);
    }

    public function LiveVersion()
    {
        return $this->belongsTo(Version::class, 'live_version_id');
    }

    public function Seo()
    {
        return $this->hasOne(ContentSeo::class);
    }

    public function getIsIndexableAttribute()
    {
        return self::handleSEOProperties($this, 'is_indexable');
    }

    public function getIsFollowableAttribute()
    {
        return self::handleSEOProperties($this, 'is_followable');
    }

    public function getUrlAttribute()
    {
        //        if(isset($this->attributes['site_id'])) {
        //            $site = Site::find($this->attributes['site_id']);
        //
        //            $module = Module::query()
        //                ->where('name', 'Teams')
        //                ->first();
        //
        //            $siteModule = SiteModule::query()
        //                ->where([
        //                    'site_id' => $site->id,
        //                    'module_id' => $module->id
        //                ])
        //                ->first();
        //
        //            if ($module && $siteModule) {
        //                $moduleSlug = $countrySlug = '';
        //                if ($siteModule->slug_url)
        //                    $moduleSlug = $siteModule->slug_url . '/';
        //                if(isset($this->country) && isset($this->country->slug))
        //                    $countrySlug = $this->country->slug . '/';
        //                return ('https://' . $site->url . '/' . $moduleSlug . $countrySlug . $this->slug);
        //            }
        //        }

        return '/';
    }

    public function blocks()
    {
        return $this->hasMany(BlockItem::class);
    }

    public function fixtures(): HasMany
    {
        return $this
            ->hasMany(Fixture::class, 'team_home', $this->id)
            ->orWhere('team_away', $this->id);
    }

    public function hasLiveHome()
    {
        return $this->hasMany(Fixture::class, 'team_home', $this->id)->where('is_live', true);
    }

    public function hasLiveAway()
    {
        return $this->hasMany(Fixture::class, 'team_away', $this->id)->where('is_live', true);
    }

    public function fixturesByDate($status = null, $limit = 5)
    {
        $selfId = (int) $this->attributes['id'];
        $timezone = request()->timezone ?? config('app.timezone');
        $currentDate = Carbon::parse(Carbon::today(), $timezone)->toDateString();

        switch ($status) {
            case 'future':
                $query = Fixture::whereDate('date', '>', $currentDate)
                    ->limit($limit)
                    ->orderBy('date', 'asc')
                    ->orderBy('id', 'asc');
                break;
            case 'finished':
                $query = Fixture::whereDate('date', '<', $currentDate)
                    ->limit($limit)
                    ->orderBy('date', 'desc')
                    ->orderBy('id', 'asc');
                break;
            default:
                $query = Fixture::whereDate('date', $status ?? $currentDate)
                    ->orderBy('date', 'asc')
                    ->orderBy('id', 'asc');
                break;
        }

        $fixtures = $query
            ->where(function ($query) use ($selfId) {
                $query
                    ->where('team_home', $selfId)
                    ->orWhere('team_away', $selfId);
            })
            ->with([
                'league' => function ($query) {
                    $query->with('country');
                }
            ])
            ->get();

        $fixtures = $this->convertFixturesTimezone($fixtures);

        return self::processFixtureData(array_values($fixtures));
    }

    public function getLimitState($state, $direction)
    {
        $selfId = (int) $this->attributes['id'];
        $timezone = request()->timezone ?? config('app.timezone');
        $currentDate = Carbon::parse(Carbon::today(), $timezone)->toDateString();
        $limit = 10;

        if ($state < 0 && $direction == 'next') {
            return true;
        } elseif ($state > 0 && $direction == 'prev') {
            return true;
        }

        if ($state < 0) {
            $status = 'finished';
            $skip = ($limit * abs($state)) + 10;
        } else {
            $status = 'future';
            $skip = ($limit * abs($state)) + 10;
        }

        switch ($status) {
            case 'future':
                $query = Fixture::whereDate('date', '>', $currentDate)
                    ->limit($limit)
                    ->skip($skip)
                    ->orderBy('date', 'asc')
                    ->orderBy('id', 'asc');
                break;
            case 'finished':
                $query = Fixture::whereDate('date', '<', $currentDate)
                    ->limit($limit)
                    ->skip($skip)
                    ->orderBy('date', 'desc')
                    ->orderBy('id', 'asc');
                break;
            default:
                break;
        }

        $fixtures = $query
            ->where(function ($query) use ($selfId) {
                $query
                    ->where('team_home', $selfId)
                    ->orWhere('team_away', $selfId);
            })
            ->get();

        if ($fixtures->count()) {
            return true;
        } else {
            return false;
        }

    }

    public function fixturesByPagination($state)
    {
        $selfId = (int) $this->attributes['id'];
        $timezone = request()->timezone ?? config('app.timezone');
        $currentDate = Carbon::parse(Carbon::today(), $timezone)->toDateString();
        $limit = 10;

        if ($state < 0) {
            $status = 'finished';
            $skip = $limit * abs($state);
        } else {
            $status = 'future';
            $skip = $limit * abs($state);
        }


        switch ($status) {
            case 'future':
                $query = Fixture::whereDate('date', '>', $currentDate)
                    ->limit($limit)
                    ->skip($skip)
                    ->orderBy('date', 'asc')
                    ->orderBy('id', 'asc');
                break;
            case 'finished':
                $query = Fixture::whereDate('date', '<', $currentDate)
                    ->limit($limit)
                    ->skip($skip)
                    ->orderBy('date', 'desc')
                    ->orderBy('id', 'asc');
                break;
            default:

                break;
        }

        $fixtures = $query
            ->where(function ($query) use ($selfId) {
                $query
                    ->where('team_home', $selfId)
                    ->orWhere('team_away', $selfId);
            })
            ->with([
                'league' => function ($query) {
                    $query->with('country');
                }
            ])
            ->get();

        $fixtures = $this->convertFixturesTimezone($fixtures);

        if ($status == 'finished') {
            usort($fixtures, function ($a, $b) {
                $dateA = strtotime($a['date']);
                $dateB = strtotime($b['date']);

                return $dateA - $dateB;
            });
        }

        return self::processFixtureData(array_values($fixtures));
    }

    public function groupFixturesByLeague($fixtures)
    {
        $page = (int) request()->query('page', 1);
        $perPage = (int) request()->query('limit', 10);
        $leagueId = request()->query('league_id', null);

        $fixturesCollection = collect($fixtures);

        // Filter by league api_id if it's provided
        if ($leagueId !== null) {
            $fixturesCollection = $fixturesCollection->where('league.id', $leagueId);
        }

        // group leagues by id
        $groupedByLeague = $fixturesCollection->groupBy('league_id');

        if ($groupedByLeague->isEmpty()) {
            return [];
        }

        // create response
        $paginated = $groupedByLeague->map(function ($items) use ($perPage, $page) {
            $totalItems = $items->count();
            $totalPages = max((int) ceil($totalItems / $perPage), 1);

            if ($page > $totalPages) {
                return [];
            }

            $items = $items->slice(($page - 1) * $perPage, $perPage);

            if ($items->isEmpty()) {
                return [];
            }

            $league = $items->first()->league;

            return [
                'id' => $league->id,
                'name' => $league->name,
                'slug' => $league->slug,
                'is_cup' => $league->is_cup,
                'country_name' => $league->country->name,
                'country_slug' => $league->country->slug,
                'country_code' => $league->country->code,
                'image_url' => ImagePathService::get($league),
                'fixtures' => self::processFixtureData(array_values($items->toArray())),
                'pagination' => [
                    'limit' => $perPage,
                    'total' => $totalItems,
                    'page' => $page,
                ],
            ];
        })->filter();

        return $paginated->values()->toArray();
    }

    public function processFixtureData($fixtures)
    {
        return array_map(fn ($fixture) => [
            'id' => $fixture['id'],
            'date' => $fixture['date'],
            'status' => $fixture['status'],
            'elapsed' => $fixture['elapsed'],
            'league_id' => $fixture['league_id'],
            'is_live' => $fixture['is_live'],
            'winner' => $fixture['winner'],
            'no_events' => $fixture['no_events'],
            'no_stadings' => $fixture['no_stadings'],
            'goals_home' => $fixture['goals_home'],
            'goals_away' => $fixture['goals_away'],
            'goals_extra_home' => $fixture['goals_extra_home'],
            'goals_extra_away' => $fixture['goals_extra_away'],
            'penalty_home' => $fixture['penalty_home'],
            'penalty_away' => $fixture['penalty_away'],
            'broadcasters' => $fixture['broadcasters'],
            'home_team' => $fixture['home_team'],
            'away_team' => $fixture['away_team'],
            'league' => [
                'id' => $fixture['league']['id'],
                'name' => $fixture['league']['name'],
                'slug' => $fixture['league']['slug'],
                'is_cup' => $fixture['league']['is_cup'],
                'country_name' => $fixture['league']['country']['name'],
                'country_slug' => $fixture['league']['country']['slug'],
                'country_code' => $fixture['league']['country']['code'],
                'image_url' => ImagePathService::get($fixture['league']),
            ],
        ], $fixtures);
    }

    public static function boot()
    {
        parent::boot();

        static::created(function ($model) {
            DB::transaction(function () use ($model) {
                $siteId = Site::whereUrl('jogoshoje.com')->first()->id;
                $model->site_id = $siteId;
                $model->save();

                $is_followable = 1;
                $is_indexable = 1;
                if ($module = Module::where('name', 'Teams')->first()) {
                    if ($siteModule = SiteModule::where('site_id', $model->attributes['site_id'])->where('module_id', $module->id)->first()) {
                        $is_followable = $siteModule->default_child_followable;
                        $is_indexable = $siteModule->default_child_indexable;
                    }
                }

                ContentSeo::create([
                    'team_id' => $model->attributes['id'],
                    'site_id' => $model->attributes['site_id'],
                    'title' => 'Jogos - ' . $model->attributes['name'] . ' ⚽ Resultados e Classificação',
                    'og_title' => 'Jogos - ' . $model->attributes['name'] . ' ⚽ Resultados e Classificação',
                    'description' => 'Classificação, Resultados e Próximos jogos - ' . $model->attributes['name'] . ' ✅ Atualizado e em primeira mão!',
                    'og_description' => 'Classificação, Resultados e Próximos jogos - ' . $model->attributes['name'] . ' ✅ Atualizado e em primeira mão!',
                    // 'og_image_id'    => $model->attributes['media_site_id'], # We don't have an image in our bucket during team creation
                    'canonical_url' => $model->getUrlAttribute(),
                    'is_followable' => $is_followable,
                    'is_indexable' => $is_indexable
                ]);

                $version = Version::firstOrNew(['site_id' => $model->attributes['site_id'], 'team_id' => $model->attributes['id']]);
                $version->description = 'Initial version';
                $version->save();

                Team::query()
                    ->where('id', $model->attributes['id'])
                    ->update([
                        'version_id' => $version->id
                    ]);

                return true;
            });
        });

        static::saved(function ($model) {
            if ($seo = ContentSeo::query()->where('team_id', $model->attributes['id'])->first()) {
                $seo->fillBasics($model);
            }
        });
    }

    public function findByApiId($apiId): ?Builder
    {
        if (empty($apiId)) {
            return null;
        }

        return self::where('api_id', $apiId);
    }

    public function getTeamsForSquadUpdate(int $limit, string $squadsAt)
    {
        return self::select(['api_id', 'id'])
            ->where(function ($q) use ($squadsAt) {
                $q->whereDate('squads_at', '<', $squadsAt)
                    ->orWhereNull('squads_at');
            })
            ->orderBy('api_id', 'ASC')
            ->limit($limit);
    }

    public function persistTeam(array $data): void
    {
        $model = self::firstOrNew(['api_id' => $data['create']['api_id']]);
        $model->fill($model->exists ? $data['update'] : $data['create']);
        $model->save();
    }

    public function venue(): BelongsTo
    {
        return $this->belongsTo(Venue::class, 'venue_id');
    }

    public function competitions(): HasMany
    {
        return $this
            ->hasMany(LeagueTeam::class)
            ->join('leagues', 'leagues.id', '=', 'league_teams.league_id')
            ->join('countries', 'countries.id', '=', 'leagues.country_id')
            ->join('league_seasons', function ($join) {
                $join->on('league_seasons.league_id', '=', 'leagues.id');
                $join->on('league_seasons.current', '=', DB::raw("1"));
            })
            ->join('seasons', 'seasons.id', '=', 'league_seasons.season_id')
            ->whereRaw('league_teams.season_id = league_seasons.season_id')
            ->groupBy('league_teams.league_id')
            ->select([
                DB::raw('leagues.*'),
                DB::raw('countries.name country_name'),
                DB::raw('countries.slug country_slug'),
                DB::raw("(SELECT IF(winner = {$this->id}, 1,0) FROM league_seasons WHERE league_id = leagues.id AND season_id = (GROUP_CONCAT(DISTINCT seasons.id) - 1)) winner_last_season"),
            ]);
    }

    public function coach(): HasOne
    {
        return $this
            ->hasOne(FixtureLineup::class)
            ->orderBy('id', 'desc')
            ->limit(1);
    }

    public function getBySlug(string $countrySlug, string $teamSlug): ?Team
    {
        return self::join('countries', 'countries.id', '=', 'teams.country_id')
            ->where('teams.slug', $teamSlug)
            ->where('countries.slug', $countrySlug)
            ->select(['*', DB::raw('teams.id as id'), DB::raw('teams.slug as slug'), DB::raw('teams.name as name'), DB::raw('teams.media_site_id as media_site_id')])
            ->first();
    }

    public function standings(): HasMany
    {
        return $this->hasMany(Standing::class)
            ->join('league_seasons', function ($join) {
                $join->on("league_seasons.season_id", '=', "standings.season_id")
                    ->where("league_seasons.current", DB::raw("1"));
            })
            ->join(DB::raw("(
                    SELECT
                        ls.season_id
                    FROM league_teams lt
                    JOIN league_seasons ls ON ls.league_id = lt.league_id AND ls.current = 1
                    WHERE
                        lt.team_id = " . $this->id . " AND
                        lt.season_id = ls.season_id
                    GROUP BY ls.season_id
                    ORDER BY ls.season_id DESC
                    LIMIT 1
                ) max_ls"), function ($join) {
                $join->on('max_ls.season_id', '=', 'league_seasons.season_id');
            })
            ->whereRaw("standings.season_id = max_ls.season_id")
            ->groupByRaw('standings.team_id, standings.season_id, standings.league_id');
    }

    public function players(): HasMany
    {
        return $this->hasMany(TeamPlayer::class)
            ->join('league_seasons', function ($join) {
                $join->on('league_seasons.league_id', '=', 'team_players.league_id');
                $join->on('league_seasons.current', '=', DB::raw("1"));
            })
            ->join(DB::raw("(
                    SELECT
                        ls.season_id
                    FROM league_teams lt
                    JOIN league_seasons ls ON ls.league_id = lt.league_id AND ls.current = 1
                    WHERE
                        lt.team_id = " . $this->id . " AND
                        lt.season_id = ls.season_id
                    GROUP BY ls.season_id
                    ORDER BY ls.season_id DESC
                    LIMIT 1
                ) max_ls"), function ($join) {
                $join->on('max_ls.season_id', '=', 'league_seasons.season_id');
            })
            ->whereRaw("team_players.season_id = max_ls.season_id");
    }

    public function favorites(): MorphMany
    {
        return $this->morphMany(User::class, 'favoritable');
    }

    public function searchableAs(): string
    {
        return $this->getIndexPrefixByEnvironment() . 'teams';
    }

    public function searchable(): bool
    {
        return true;
    }

    public function toSearchableArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->api_name,
            'slug' => $this->slug,
            'country_id' => $this->country_id,
            'national' => $this->national,
            'api_id' => $this->api_id,
            'media_site_id' => $this->media_site_id,
            'is_favorite' => $this->is_favorite,
            'popularity' => $this->popularity,
        ];
    }

    public function getSearchFilterAttributes(): array
    {
        return [
            'name',
            'slug',
        ];
    }

    public function increasePopularity(): void
    {
        $this->increment('popularity');
    }
}
