<?php

namespace App\Models;

use App\Models\JogosHoje\{League, Team};
use App\Traits\CustomRedisTrait;
use Illuminate\Auth\Passwords\CanResetPassword;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\{HasMany, MorphToMany};
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\{Crypt, Hash, Session};
use Illuminate\Support\Str;
use Laravel\Sanctum\HasApiTokens;
use Watson\Validating\ValidatingTrait;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens;
    use HasFactory;
    use Notifiable;
    use GuiidyModel;
    use ValidatingTrait;
    use SoftDeletes;
    use CustomRedisTrait;
    use CanResetPassword;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'is_active',
        'timezone_id',
        'is_internal',
        'role_id',
        'media_id',
        'bio',
        'title',
        'short_bio',
        'twitter',
        'linkedin',
        'alternative_email',
        'discord',
        'site',
        'highlights',
        'facebook',
        'facebook_id',
        'google_id',
        'slug',
        'awards',
        'occupation',
        'education',
        'telephone',
        'knowsabout',
        'dob',
        'state',
        'full_name',
        'apple_id',
    ];

    protected ?array $rules = [
        'email' => 'required|email|unique:users,email',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'is_active' => 'boolean',
        'is_internal' => 'boolean',
        'is_impersonated' => 'boolean'
    ];

    protected $appends = [
        'first_letter',
        'is_impersonated',
        'short_bio',
        'bio',
        'title',
        'twitter',
        'linkedin',
        'alternative_email',
        'discord',
        'site',
        'highlights',
        'url',
        'awards',
        'facebook',
        'occupation',
        'education',
        'telephone',
        'knowsabout',
        'token',
    ];

    public function getTokenAttribute()
    {
        return Crypt::encryptString($this->attributes['id']);
    }

    public function getIsActiveAttribute()
    {
        return !empty($this->attributes['is_active']);
    }

    public function SiteUsers()
    {
        return $this->hasMany(SiteUser::class)->with(['Role']);
    }

    public function logins(): HasMany
    {
        return $this->hasMany(UserLogin::class);
    }

    public function getIsGodAttribute()
    {
        if (!$this->Role) {
            return false;
        }
        return $this->Role->value === 'GOD';
    }

    public function getFirstLetterAttribute()
    {
        return ucfirst(mb_substr($this->attributes['name'], 0, 1));
    }

    public function getUrlAttribute()
    {
        $site = currentSite();

        if (!$site) {
            return '';
        }

        $page = Page::query()
            ->where('site_id', $site->id)
            ->where('is_about_us', 1)
            ->first(['slug_url']);

        if (!$page) {
            return '';
        }

        return '/' . $page->slug_url . (!empty($this->attributes['slug']) ? ('/' . $this->attributes['slug']) : '');
    }

    public function getIsImpersonatedAttribute()
    {
        return Session::get('originalUser', false);
    }

    public function Role()
    {
        return $this->belongsTo(Role::class)->with(['Permissions']);
    }

    public function Avatar()
    {
        return $this->belongsTo(Media::class, 'media_id');
    }

    public function getShortBioAttribute()
    {
        return !empty($this->attributes['short_bio']) ? $this->attributes['short_bio'] : '';
    }

    public function getBioAttribute()
    {
        return !empty($this->attributes['bio']) ? $this->attributes['bio'] : '';
    }

    public function gettitleAttribute()
    {
        return !empty($this->attributes['title']) ? $this->attributes['title'] : '';
    }

    public function getTwitterAttribute()
    {
        return !empty($this->attributes['twitter']) ? $this->attributes['twitter'] : '';
    }

    public function getFacebookAttribute()
    {
        return !empty($this->attributes['facebook']) ? $this->attributes['facebook'] : '';
    }

    public function getOccupationAttribute()
    {
        return !empty($this->attributes['occupation']) ? $this->attributes['occupation'] : '';
    }

    public function getEducationAttribute()
    {
        return !empty($this->attributes['education']) ? $this->attributes['education'] : '';
    }

    public function getTelephoneAttribute()
    {
        return !empty($this->attributes['telephone']) ? $this->attributes['telephone'] : '';
    }

    public function getDiscordAttribute()
    {
        return !empty($this->attributes['discord']) ? $this->attributes['discord'] : '';
    }

    public function getAlternativeEmailAttribute()
    {
        return !empty($this->attributes['alternative_email']) ? $this->attributes['alternative_email'] : '';
    }

    public function getSiteAttribute()
    {
        return !empty($this->attributes['site']) ? $this->attributes['site'] : '';
    }

    public function getLinkedinAttribute()
    {
        return !empty($this->attributes['linkedin']) ? $this->attributes['linkedin'] : '';
    }

    public function getKnowsaboutAttribute()
    {
        return !empty($this->attributes['knowsabout']) ? $this->attributes['knowsabout'] : '';
    }

    public function getHighlightsAttribute()
    {
        if (empty($this->attributes['highlights'])) {
            return [];
        }

        return json_decode($this->attributes['highlights']);
    }

    public function setHighlightsAttribute($value)
    {
        if (!empty($value)) {
            array_walk_recursive($value, function (&$item, $key) {
                $item = null === $item ? '' : $item;
            });
        }
        $this->attributes['highlights'] = json_encode($value);
    }

    public function getAwardsAttribute()
    {
        if (empty($this->attributes['awards'])) {
            return [];
        }

        return json_decode($this->attributes['awards']);
    }

    public function setAwardsAttribute($value)
    {
        if (!empty($value)) {
            array_walk_recursive($value, function (&$item, $key) {
                $item = null === $item ? '' : $item;
            });
        }
        $this->attributes['awards'] = json_encode($value);
    }

    public static function boot()
    {
        parent::boot();

        static::saved(function ($model) {
            User::query()
                ->where('id', $model->attributes['id'])
                ->update(['slug' => Str::slug($model->attributes['name'])]);
        });
    }

    public static function createUserForSite(array $data, Site $site): self
    {
        $data = [
            'name' => $data['name'] ?? null,
            'email' => strtolower(trim($data['email'])),
            'password' => isset($data['password']) ? Hash::make($data['password']) : null,
            'is_internal' => 0,
        ];
        $user = self::withTrashed()->whereEmail($data['email'])->first();
        if ($user) {
            $user->restore();
            $user->setAttribute('name', $data['name']);
            $user->setAttribute('password', $data['password']);
            $user->save();
        } else {
            $user = self::create($data);
        }

        $role = Role::whereValue('USER')->first();
        if ($role) {
            $user->SiteUsers()->firstOrCreate([
                'role_id' => $role->id,
                'site_id' => $site->id
            ]);
        }

        return $user;
    }

    public function favoriteLeagues(): MorphToMany
    {
        return $this->morphedByMany(League::class, 'favoritable', 'user_favorites');
    }

    public function favoriteTeams(): MorphToMany
    {
        return $this->morphedByMany(Team::class, 'favoritable', 'user_favorites');
    }
    public function favoriteBettingSites(): MorphToMany
    {
        return $this->morphedByMany(BettingSite::class, 'favoritable', 'user_favorites');
    }
}
