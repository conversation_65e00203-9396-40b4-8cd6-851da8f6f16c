<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SetSessionDomain
{
    /**
     * Handle an incoming request.
     *
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (in_array($request->getHost(), ['api.jogoshoje.com', 'dev-api.jogoshoje.com', 'qa-api.jogoshoje.com', 'staging-api.jogoshoje.com', 'preview-api.jogoshoje.com'])) {
            config(['session.domain' => '.jogoshoje.com'/*, 'session.secure' => true*/]);
        }

        return $next($request);
    }
}
