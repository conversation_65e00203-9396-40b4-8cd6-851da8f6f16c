<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class BetanoRateLimitMiddleware
{
    /**
     * Rate limit for Betano API calls
     * Implements 5-minute cache as suggested in the guide
     */
    public function handle(Request $request, Closure $next)
    {
        $cacheKey = 'betano_rate_limit_' . $request->ip();
        $maxRequests = 12; // 12 requests per 5 minutes (1 every 25 seconds)
        $timeWindow = 300; // 5 minutes in seconds

        // Get current request count for this IP
        $requestCount = Cache::get($cacheKey, 0);

        if ($requestCount >= $maxRequests) {
            Log::warning('[BETANO_RATE_LIMIT] Rate limit exceeded', [
                'ip' => $request->ip(),
                'requests' => $requestCount,
                'max' => $maxRequests
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Rate limit exceeded',
                'message' => 'Too many requests to Betano API. Please try again later.',
                'retry_after' => $timeWindow
            ], 429);
        }

        // Increment request count
        Cache::put($cacheKey, $requestCount + 1, $timeWindow);

        // Add rate limit headers to response
        $response = $next($request);
        
        $response->headers->set('X-RateLimit-Limit', $maxRequests);
        $response->headers->set('X-RateLimit-Remaining', max(0, $maxRequests - $requestCount - 1));
        $response->headers->set('X-RateLimit-Reset', now()->addSeconds($timeWindow)->timestamp);

        return $response;
    }
}
