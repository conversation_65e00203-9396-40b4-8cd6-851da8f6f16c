<?php

namespace App\Http\Resources\JogosHoje;

use App\Models\MediaSite;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SearchResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $type = strtolower(class_basename($this->resource));

        $baseData = [
            'id' => $this->id,
            'elapsed' => $this->elapsed ?? null,
            'is_live' => $this->is_live ?? 0,
            'is_cup' => !is_null($this->is_cup) ? (int) $this->is_cup : null,
            'country_slug' => in_array($type, ['league', 'team']) ? $this->country?->slug : ($this->league?->country?->slug ?? null),
            'country_name' => in_array($type, ['league', 'team']) ? $this->country?->name : ($this->league?->country?->name ?? null),
            'league_slug' => $type == 'league' ? $this->slug : ($this->league?->slug ?? null),
            'league_name' => $type == 'league' ? $this->name : ($this->league?->name ?? null),
            'team_slug' => $type == 'team' ? $this->slug : null,
            'team_name' => $type == 'team' ? $this->name : null,
            'media_site_id' => $this->media_site_id ?? null,
            'country_media_site_id' => in_array($type, ['league', 'team']) ? $this->country?->media_site_id : ($this->league?->country?->media_site_id ?? null),
            'league_media_site_id' => $type == 'league' ? $this->media_site_id : ($this->league?->media_site_id ?? null),
            'date' => $this->date ?? null,
            'type' => ((string) $type) . 's',
            'title' => match ($type) {
                'fixture' => 'Jogos',
                'team'    => 'Times',
                'league'  => 'Campeonatos',
                default   => null,
            },
            'goals_home' => $this->goals_home ? ((int) $this->goals_home + (int) $this->goals_extra_home) : null,
            'goals_away' => $this->goals_away ? ((int) $this->goals_away + (int) $this->goals_extra_away) : null,
            'status' => $this->status ?? null,
            'media' => null,
        ];

        if ($baseData['media_site_id']) {
            switch ($type) {
                case 'team': {
                    $baseData['media'] = new LogoResource(MediaSite::find($baseData['media_site_id']));
                    break;
                }
                case 'league':
                    $baseData['media'] = $baseData['league_media_site_id'] ? new LogoResource(MediaSite::find($baseData['league_media_site_id'])) : null;
                    break;
                default:
                    break;
            }
        }

        if ($baseData['country_media_site_id']) {
            $baseData['country_media'] = new LogoResource(MediaSite::find($baseData['country_media_site_id']));
        }
        
        if ($type == 'fixture') {
            $homeTeam = $this->homeTeam;
            $awayTeam = $this->awayTeam;

            $baseData['home_team'] = [
                'name' => $homeTeam->name,
                'slug' => $homeTeam->slug,
                'media' => $homeTeam->media_site_id ? new LogoResource(MediaSite::find($homeTeam->media_site_id)) : null,
            ];

            $baseData['away_team'] = [
                'name' => $awayTeam->name,
                'slug' => $awayTeam->slug,
                'media' => $awayTeam->media_site_id ? new LogoResource(MediaSite::find($awayTeam->media_site_id)) : null,
            ];

            $baseData['media'] = $baseData['league_media_site_id'] ? new LogoResource(MediaSite::find($baseData['league_media_site_id'])) : null;
        }

        return $baseData;
    }
}