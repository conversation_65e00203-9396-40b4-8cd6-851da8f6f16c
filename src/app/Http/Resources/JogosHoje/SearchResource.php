<?php

namespace App\Http\Resources\JogosHoje;

use App\Models\MediaSite;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SearchResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $type = strtolower(class_basename($this->resource));

        $baseData = [
            'id' => $this->id,
            'elapsed' => $this->elapsed ?? null,
            'is_live' => $this->is_live ?? 0,
            'is_cup' => $this->is_cup ?? null,
            'country_slug' => $this->getCountrySlug(),
            'country_name' => $this->getCountryName(),
            'league_slug' => $this->getLeagueSlug(),
            'league_name' => $this->getLeagueName(),
            'team_slug' => $this->getTeamSlug(),
            'team_name' => $this->getTeamName(),
            'media_site_id' => $this->getMediaSiteId(),
            'country_media_site_id' => $this->getCountryMediaSiteId(),
            'league_media_site_id' => $this->getLeagueMediaSiteId(),
            'date' => $this->date ?? null,
            'type' => $type,
            'title' => $this->getTitle(),
            'goals_home' => $this->goals_home ? ((int) $this->goals_home + (int) $this->goals_extra_home) : null,
            'goals_away' => $this->goals_away ? ((int) $this->goals_away + (int) $this->goals_extra_away) : null,
            'status' => $this->status ?? null,
        ];

        // Handle media based on type
        if ($type === 'fixture') {
            return array_merge($baseData, $this->getFixtureSpecificData());
        } else {
            return array_merge($baseData, [
                'media' => $this->getMediaForNonFixture(),
                'country_media' => $this->getCountryMediaForNonFixture(),
            ]);
        }
    }

    private function getCountrySlug(): string
    {
        // For fixtures - get from league.country relationship
        if (isset($this->league) && isset($this->league->country)) {
            return $this->league->country->slug ?? '';
        }

        // For teams - get country from direct relationship first
        if (isset($this->country)) {
            return $this->country->slug ?? '';
        }

        // For teams - get country from first league relationship
        if (isset($this->leagues) && $this->leagues->isNotEmpty()) {
            $firstLeague = $this->leagues->first();
            if (isset($firstLeague->country)) {
                return $firstLeague->country->slug ?? '';
            }
        }

        return '';
    }

    private function getCountryName(): string
    {
        // For fixtures - get from league.country relationship
        if (isset($this->league) && isset($this->league->country)) {
            return $this->league->country->name ?? '';
        }

        // For teams - get country from direct relationship first
        if (isset($this->country)) {
            return $this->country->name ?? '';
        }

        // For teams - get country from first league relationship
        if (isset($this->leagues) && $this->leagues->isNotEmpty()) {
            $firstLeague = $this->leagues->first();
            if (isset($firstLeague->country)) {
                return $firstLeague->country->name ?? '';
            }
        }

        return '';
    }

    private function getLeagueSlug(): string
    {
        // For fixtures - get from league relationship
        if (isset($this->league)) {
            return $this->league->slug ?? '';
        }

        // For teams - get first league from relationship
        if (isset($this->leagues) && $this->leagues->isNotEmpty()) {
            return $this->leagues->first()->slug ?? '';
        }

        // For leagues - use own slug
        if (isset($this->slug)) {
            return $this->slug;
        }

        return '';
    }

    private function getLeagueName(): string
    {
        // For fixtures - get from league relationship
        if (isset($this->league)) {
            return $this->league->name ?? '';
        }

        // For teams - get first league from relationship
        if (isset($this->leagues) && $this->leagues->isNotEmpty()) {
            return $this->leagues->first()->name ?? '';
        }

        // For leagues - use own name
        if (isset($this->name)) {
            return $this->name;
        }

        return '';
    }

    private function getTeamSlug(): string
    {
        // For teams and leagues - use own slug
        if (isset($this->slug)) {
            return $this->slug;
        }

        return '';
    }

    private function getTeamName(): string
    {
        // For teams and leagues - use own name
        if (isset($this->name)) {
            return $this->name;
        }

        return '';
    }

    private function getMediaSiteId(): string
    {
        // For teams and leagues, use their own media_site_id
        if (isset($this->media_site_id)) {
            return (string) $this->media_site_id;
        }

        return '';
    }

    private function getCountryMediaSiteId(): string
    {
        // For fixtures - get from league.country relationship
        if (isset($this->league) && isset($this->league->country)) {
            return (string) ($this->league->country->media_site_id ?? '');
        }

        // For teams - get country media_site_id from direct relationship first
        if (isset($this->country)) {
            return (string) ($this->country->media_site_id ?? '');
        }

        // For teams - get country media_site_id from first league relationship
        if (isset($this->leagues) && $this->leagues->isNotEmpty()) {
            $firstLeague = $this->leagues->first();
            if (isset($firstLeague->country)) {
                return (string) ($firstLeague->country->media_site_id ?? '');
            }
        }

        return '';
    }

    private function getLeagueMediaSiteId(): string
    {
        // For fixtures - get from league relationship
        if (isset($this->league)) {
            return (string) ($this->league->media_site_id ?? '');
        }

        // For teams - get league media_site_id from first league relationship
        if (isset($this->leagues) && $this->leagues->isNotEmpty()) {
            return (string) ($this->leagues->first()->media_site_id ?? '');
        }

        // For leagues - use own media_site_id
        if (isset($this->media_site_id)) {
            return (string) $this->media_site_id;
        }

        return '';
    }

    private function getTitle(): string
    {
        if (isset($this->title)) {
            return $this->title;
        }
        
        // For fixtures
        if (isset($this->homeTeam) && isset($this->awayTeam)) {
            return $this->homeTeam->name . ' vs ' . $this->awayTeam->name;
        }
        
        // For teams or leagues
        if (isset($this->name)) {
            return $this->name;
        }
        
        return '';
    }

    private function getFixtureSpecificData(): array
    {
        return [
            'home_team' => [
                'name' => $this->homeTeam->name ?? '',
                'slug' => $this->homeTeam->slug ?? '',
                'media' => $this->homeTeam && $this->homeTeam->media_site_id
                    ? new LogoResource(MediaSite::find($this->homeTeam->media_site_id))
                    : null,
            ],
            'away_team' => [
                'name' => $this->awayTeam->name ?? '',
                'slug' => $this->awayTeam->slug ?? '',
                'media' => $this->awayTeam && $this->awayTeam->media_site_id
                    ? new LogoResource(MediaSite::find($this->awayTeam->media_site_id))
                    : null,
            ],
            'media' => $this->getLeagueMediaSiteId()
                ? new LogoResource(MediaSite::find($this->getLeagueMediaSiteId()))
                : null,
            'country_media' => $this->getCountryMediaSiteId()
                ? new LogoResource(MediaSite::find($this->getCountryMediaSiteId()))
                : null,
        ];
    }

    private function getMediaForNonFixture()
    {
        $mediaSiteId = $this->getMediaSiteId();
        return $mediaSiteId ? new LogoResource(MediaSite::find($mediaSiteId)) : null;
    }

    private function getCountryMediaForNonFixture()
    {
        $countryMediaSiteId = $this->getCountryMediaSiteId();
        return $countryMediaSiteId ? new LogoResource(MediaSite::find($countryMediaSiteId)) : null;
    }
}
