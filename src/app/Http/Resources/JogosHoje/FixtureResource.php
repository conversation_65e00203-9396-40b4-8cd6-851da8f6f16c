<?php

namespace App\Http\Resources\JogosHoje;

use App\Traits\FixturesTrait;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FixtureResource extends JsonResource
{
    use FixturesTrait;

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'status' => $this->status,
            'elapsed' => $this->elapsed,
            'full_date' => Carbon::parse($this->date)->timezone($request->timezone)->format('Y-m-d H:i:s'),
            'date' => Carbon::parse($this->date)->timezone($request->timezone)->format('d/m/Y'),
            'hour' => Carbon::parse($this->date)->timezone($request->timezone)->format('H:i'),
            'winner' => $this->winner,
            'is_live' => $this->is_live,
            'goals_home' => $this->goals_home,
            'goals_away' => $this->goals_away,
            'goals_extra_home' => $this->goals_extra_home,
            'goals_extra_away' => $this->goals_extra_away,
            'penalty_home' => $this->penalty_home,
            'penalty_away' => $this->penalty_away,
            'broadcasters' => $this->broadcasters,
            'home_team' => new TeamResourceSmall($this->homeTeam),
            'away_team' => new TeamResourceSmall($this->awayTeam),
            'league' => new LeagueResourceSmall($this->league),
            'predictions' => $this->predictions ?? [],
            'the_sports_id' => $this->the_sports_id,
            'environment' => $this->environment,
            'events' => $this->parseEvents($this->resource, ['goal']),
            'cards_home' => $this->cards_home,
            'cards_away' => $this->cards_away,
            'corners_home' => $this->corners_home,
            'corners_away' => $this->corners_away,
            'has_predictions' => $this->has_predictions,
            'has_lineups' => $this->has_lineups,
            'has_history' => $this->hasHistory(),
            'extra_1H' => $this->extra_1H,
            'extra_2H' => $this->extra_2H,
            'round' => $this->round,
            '_round' => $this->_round,
        ];
    }
}
