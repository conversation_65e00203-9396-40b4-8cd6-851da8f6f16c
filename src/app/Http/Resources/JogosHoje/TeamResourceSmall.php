<?php

namespace App\Http\Resources\JogosHoje;

use App\Services\JogosHoje\ImagePathService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TeamResourceSmall extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'image_url' => ImagePathService::get($this->resource),
            'country' => new CountryResource($this->country),
            'next_match' => $this->next_match ?? [],
            'previous_matches' => $this->previous_matches ?? [],
        ];
    }
}
