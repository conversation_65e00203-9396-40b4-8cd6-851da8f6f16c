<?php

namespace App\Http\Controllers\JogosHoje\Api\V3;

use App\Http\Controllers\Controller;
use App\Models\JogosHoje\Fixture;
use App\Services\JogosHoje\{ValueBetService, BetometerStreaksService};
use Illuminate\Http\{JsonResponse, Request};

class FixturesBetometerValueBetsController extends Controller
{
    public function __construct(private readonly ValueBetService $service, private readonly BetometerStreaksService $streaksService)
    {
    }

    public function __invoke(Request $request, Fixture $fixture): JsonResponse
    {
        // Get original odds as base (odds table)
        $models = collect($this->service->getValueBetsForFixture($fixture->id));

        // Use your query to map statistics and odds by market_id for accurate calculations
        $statisticsWithOdds = \Illuminate\Support\Facades\DB::select("
            SELECT
                s.*,
                sd.value,
                sd.count,
                sd.config_pretty_name,
                sd.pretty_name,
                sd.team_id,
                o.*
            FROM
                statistics s
            JOIN
                statistic_details sd ON sd.id = s.statistic_details_1_id
            LEFT JOIN
                odds o ON o.fixture_id = s.fixture_id AND o.market_id = s.market_id
            WHERE
                s.fixture_id = ?
        ", [$fixture->id]);

        // Convert to collection for easier manipulation
        $statisticsCollection = collect($statisticsWithOdds);

        // Get all raw API odds for this fixture
        $rawOdds = $this->service->fetchRawOddsForFixture($fixture);

        $valueBetsData = $models->map(function (\App\Models\Odd $odd) use ($fixture, $statisticsCollection, $rawOdds) {
            // Find the corresponding statistic data for this market_id
            $statisticData = $statisticsCollection->where('market_id', $odd->market_id)->first();

            // If we don't find statistic data for this market_id, use default values
            if (!$statisticData) {
                return [
                    'true_probability' => 0,
                    'market'           => $odd->market_id == 1 ? '1x2' : 'Other',
                    'type'             => 'unknown',
                    'fair_odd'         => 0,
                    'api_odd'          => 0,
                    'ev_calculation'   => -1,
                    'verdict'          => '',
                    // Debug
                    'market_id'        => $odd->market_id,
                    'bookmaker_id'     => $odd->bookmaker_id,
                    'original_odd'     => $odd->odd_value,
                    'debug_message'    => 'No statistic data found for this market_id',
                ];
            }

            // True Probability: value/count from statistic_details (mapped by market_id)
            $value = $statisticData->value ?? 0;
            $count = $statisticData->count ?? 0;
            $trueProbability = $count > 0 ? $value / $count : 0.0;

            // Market: if config_pretty_name is "Vitórias" then it's "1x2"
            $configPrettyName = $statisticData->config_pretty_name ?? '';
            $marketType = $configPrettyName === 'Vitórias' ? '1x2' : 'Other';

            // Type: from statistics table
            $statisticType = $statisticData->type ?? 'unknown';

            // Fair odd: 1 / true_probability
            $fairOdd = $trueProbability > 0 ? 1 / $trueProbability : 0.0;

            // API odd: get the highest odd from rawOdds for this market_id and bookmaker_id
            $apiOdd = $this->getApiOddFromRawData($rawOdds, $odd->market_id, $odd->bookmaker_id);

            // If api_odd is 0, use the original_odd as fallback
            if ($apiOdd == 0) {
                $apiOdd = (float) $odd->odd_value;
            }

            // EV Calculation: (true_probability * api_odd) - 1
            $evCalculation = ($trueProbability * $apiOdd) - 1;

            // Verdict: based on type and EV > 0
            $verdict = '';
            if ($statisticType === 'single' && $evCalculation > 0) {
                $verdict = 'Trending bet';
            } elseif ($statisticType === 'combined' && $evCalculation > 0) {
                $verdict = 'Hot bet';
            }

            return [
                'true_probability' => round($trueProbability, 4),
                'market'           => $marketType,
                'type'             => $statisticType,
                'fair_odd'         => round($fairOdd, 2),
                'api_odd'          => $apiOdd,
                'ev_calculation'   => round($evCalculation, 4),
                'verdict'          => $verdict,
                // Debug
                'market_id'        => $odd->market_id,
                'bookmaker_id'     => $odd->bookmaker_id,
                'original_odd'     => $odd->odd_value,
                'config_pretty_name' => $configPrettyName,
                'pretty_name'      => $statisticData->pretty_name ?? '',
                'value'            => $value,
                'count'            => $count,
                'statistic_type'   => $statisticType,
            ];
        });

        // Debug info
        $debugInfo = [
            'fixture_id' => $fixture->id,
            'odds_count' => $models->count(),
            'statistics_with_odds_count' => $statisticsCollection->count(),
            'value_bets_generated' => $valueBetsData->count(),
            'statistics_sample' => $statisticsCollection->take(3)->map(function($stat) {
                return [
                    'market_id' => $stat->market_id,
                    'type' => $stat->type,
                    'value' => $stat->value,
                    'count' => $stat->count,
                    'config_pretty_name' => $stat->config_pretty_name ?? '',
                    'pretty_name' => $stat->pretty_name ?? '',
                ];
            }),
        ];

        // Get streaks specific to this fixture using your query
        $fixtureStreaks = \Illuminate\Support\Facades\DB::select("
            SELECT s.*, sd.value, sd.count
            FROM statistics s
            JOIN statistic_details sd ON sd.id = s.statistic_details_1_id
            WHERE s.fixture_id = ?
        ", [$fixture->id]);

        // Get all raw API odds for this fixture
        $rawOdds = $this->service->fetchRawOddsForFixture($fixture);

        $summary = [
            'total_value_bets'       => $valueBetsData->count(),
            'positive_ev_bets'       => $valueBetsData->where('ev_calculation', '>', 0)->count(),
            'trending_bets'          => $valueBetsData->where('verdict', 'Trending bet')->count(),
            'hot_bets'               => $valueBetsData->where('verdict', 'Hot bet')->count(),
        ];

        return response()->json([
            'fixture' => [
                'id'        => $fixture->id,
                'home_team' => $fixture->homeTeam?->name,
                'away_team' => $fixture->awayTeam?->name,
                'date'      => $fixture->date,
            ],
            'value_bets' => $valueBetsData->all(),
            'api_odds'   => $rawOdds,
            'summary'    => $summary,
            'streaks'    => $fixtureStreaks,
            'debug'      => $debugInfo,
        ]);
    }

    /**
     * Get API odd from raw odds data for specific market_id and bookmaker_id
     */
    private function getApiOddFromRawData(array $rawOdds, int $marketId, int $bookmakerId): float
    {
        foreach ($rawOdds as $entry) {
            if (empty($entry->bookmakers)) {
                continue;
            }

            foreach ($entry->bookmakers as $bookmaker) {
                if ($bookmaker->id != $bookmakerId || empty($bookmaker->bets)) {
                    continue;
                }

                foreach ($bookmaker->bets as $bet) {
                    if ($bet->id != $marketId || empty($bet->values)) {
                        continue;
                    }

                    // Get the highest odd from this market
                    $maxOdd = 0.0;
                    foreach ($bet->values as $value) {
                        $oddValue = (float) $value->odd;
                        if ($oddValue > $maxOdd) {
                            $maxOdd = $oddValue;
                        }
                    }

                    return $maxOdd;
                }
            }
        }

        return 0.0;
    }
}