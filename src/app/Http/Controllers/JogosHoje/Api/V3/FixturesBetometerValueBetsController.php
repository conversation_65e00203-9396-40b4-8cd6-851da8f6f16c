<?php

namespace App\Http\Controllers\JogosHoje\Api\V3;

use App\Http\Controllers\Controller;
use App\Models\JogosHoje\{Fixture, Market};
use App\Services\JogosHoje\{ValueBetService, BetometerStreaksService};
use Illuminate\Http\{JsonResponse, Request};

class FixturesBetometerValueBetsController extends Controller
{
    public function __construct(private readonly ValueBetService $service, private readonly BetometerStreaksService $streaksService)
    {
    }

    public function __invoke(Request $request, Fixture $fixture): JsonResponse
    {
        $valueBets = $this->service->getValueBetsForFixture($fixture->id);
        $this->streaksService->handle([
            'homeTeamId' => $fixture->team_home,
            'awayTeamId' => $fixture->team_away,
        ]);

        $valueBetsData = $valueBets->map(function (\App\Models\Odd $odd) use ($fixture) {
            // Obtener datos estadísticos para este market_id usando el método original
            $statisticData = $this->service->getStatisticData($fixture, $odd->market_id);

            // True Probability: value/count de statistic_details
            $trueProbability = $statisticData['probability'] ?? 0.0;

            // Market: si config_pretty_name es "Vitórias" entonces es "1x2", sino usar el tipo del market
            $configPrettyName = $statisticData['config_pretty_name'] ?? '';
            $marketType = $configPrettyName === 'Vitórias' ? '1x2' : (Market::find($odd->market_id)?->type ?? 'Unknown');

            // Type: campo type de la tabla statistics
            $statisticType = $statisticData['type'] ?? 'unknown';

            // Fair odd: 1 / true_probability
            $fairOdd = $trueProbability > 0 ? 1 / $trueProbability : 0.0;

            // API odd: escoger el odd mayor de "1x2" basado en pretty_name
            $prettyName = $statisticData['pretty_name'] ?? '';
            $apiOdd = $prettyName ? $this->service->getMax1X2Odd($fixture, $prettyName) : 0.0;

            // EV Calculation: (true_probability * api_odd) - 1
            $evCalculation = ($trueProbability * $apiOdd) - 1;

            // Verdict: basado en type y EV > 0
            $verdict = '';
            if ($statisticType === 'single' && $evCalculation > 0) {
                $verdict = 'Trending bet';
            } elseif ($statisticType === 'combined' && $evCalculation > 0) {
                $verdict = 'Hot bet';
            }

            return [
                'outcome'          => $odd->option_name, // Mantener el outcome original
                'true_probability' => round($trueProbability, 4),
                'market'           => $marketType,
                'type'             => $statisticType,
                'fair_odd'         => round($fairOdd, 2),
                'api_odd'          => $apiOdd,
                'ev_calculation'   => round($evCalculation, 4),
                'verdict'          => $verdict,
                // Campos de debug
                'market_id'        => $odd->market_id,
                'bookmaker_id'     => $odd->bookmaker_id,
                'original_odd'     => $odd->odd_value,
                'config_pretty_name' => $configPrettyName,
                'pretty_name'      => $prettyName,
            ];
        });

        $summary = [
            'total_value_bets'         => $valueBets->count(),
            'positive_ev_bets'         => $valueBets->where('ev_positive', true)->count(),
            'average_odd'              => round($valueBets->avg('odd_value'), 2),
            'average_true_probability' => round($valueBets->avg('true_probability'), 4),
            'average_expected_value'   => round($valueBets->avg('expected_value'), 4),
            'hot_bets'                 => $valueBets->where('is_hot_bet', true)->count(),
            'trending_bets'            => $valueBets->where('is_trending_bet', true)->count(),
        ];

        return response()->json([
            'fixture' => [
                'id'        => $fixture->id,
                'home_team' => $fixture->homeTeam?->name,
                'away_team' => $fixture->awayTeam?->name,
                'date'      => $fixture->date,
            ],
            'value_bets' => $valueBetsData->all(),
            'summary'    => $summary,
            'streaks'    => $this->streaksService->result,
        ]);
    }
}