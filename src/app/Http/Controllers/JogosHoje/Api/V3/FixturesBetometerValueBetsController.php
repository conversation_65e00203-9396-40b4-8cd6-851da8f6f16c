<?php

namespace App\Http\Controllers\JogosHoje\Api\V3;

use App\Http\Controllers\Controller;
use App\Models\JogosHoje\{Fixture, Market};
use App\Services\JogosHoje\{ValueBetService, BetometerStreaksService};
use Illuminate\Http\{JsonResponse, Request};

class FixturesBetometerValueBetsController extends Controller
{
    public function __construct(private readonly ValueBetService $service, private readonly BetometerStreaksService $streaksService)
    {
    }

    public function __invoke(Request $request, Fixture $fixture): JsonResponse
    {
        // Buscar TODAS las statistics para este fixture SIN FILTROS
        $statistics = \App\Models\JogosHoje\Statistic::where('fixture_id', $fixture->id)
            ->with(['statisticDetailOne', 'statisticDetailTwo'])
            ->get();

        $valueBetsData = collect();

        foreach ($statistics as $statistic) {
            // Procesar statistic_details_1 si existe
            if ($statistic->statisticDetailOne) {
                $statisticDetail = $statistic->statisticDetailOne;

                // True Probability: value/count de statistic_details
                $trueProbability = $statisticDetail->count > 0 ? $statisticDetail->value / $statisticDetail->count : 0.0;

                // Market: si config_pretty_name es "Vitórias" entonces es "1x2"
                $marketType = $statisticDetail->config_pretty_name === 'Vitórias' ? '1x2' : 'Other';

                // Type: campo type de la tabla statistics
                $statisticType = $statistic->type ?? 'unknown';

                // Fair odd: 1 / true_probability
                $fairOdd = $trueProbability > 0 ? 1 / $trueProbability : 0.0;

                // API odd: escoger el odd mayor de "1x2" basado en pretty_name
                $prettyName = $statisticDetail->pretty_name ?? '';
                $apiOdd = $prettyName ? $this->service->getMax1X2Odd($fixture, $prettyName) : 0.0;

                // EV Calculation: (true_probability * api_odd) - 1
                $evCalculation = ($trueProbability * $apiOdd) - 1;

                // Verdict: basado en type y EV > 0
                $verdict = '';
                if ($statisticType === 'single' && $evCalculation > 0) {
                    $verdict = 'Trending bet';
                } elseif ($statisticType === 'combined' && $evCalculation > 0) {
                    $verdict = 'Hot bet';
                }

                $valueBetsData->push([
                    'outcome'          => $statisticDetail->pretty_name,
                    'true_probability' => round($trueProbability, 4),
                    'market'           => $marketType,
                    'type'             => $statisticType,
                    'fair_odd'         => round($fairOdd, 2),
                    'api_odd'          => $apiOdd,
                    'ev_calculation'   => round($evCalculation, 4),
                    'verdict'          => $verdict,
                    // Campos de debug
                    'statistic_id'     => $statistic->id,
                    'statistic_detail_id' => $statisticDetail->id,
                    'config_pretty_name' => $statisticDetail->config_pretty_name,
                    'value'            => $statisticDetail->value,
                    'count'            => $statisticDetail->count,
                    'team_id'          => $statisticDetail->team_id,
                ]);
            }

            // Procesar statistic_details_2 si existe
            if ($statistic->statisticDetailTwo) {
                $statisticDetail = $statistic->statisticDetailTwo;

                // True Probability: value/count de statistic_details
                $trueProbability = $statisticDetail->count > 0 ? $statisticDetail->value / $statisticDetail->count : 0.0;

                // Market: si config_pretty_name es "Vitórias" entonces es "1x2"
                $marketType = $statisticDetail->config_pretty_name === 'Vitórias' ? '1x2' : 'Other';

                // Type: campo type de la tabla statistics
                $statisticType = $statistic->type ?? 'unknown';

                // Fair odd: 1 / true_probability
                $fairOdd = $trueProbability > 0 ? 1 / $trueProbability : 0.0;

                // API odd: escoger el odd mayor de "1x2" basado en pretty_name
                $prettyName = $statisticDetail->pretty_name ?? '';
                $apiOdd = $prettyName ? $this->service->getMax1X2Odd($fixture, $prettyName) : 0.0;

                // EV Calculation: (true_probability * api_odd) - 1
                $evCalculation = ($trueProbability * $apiOdd) - 1;

                // Verdict: basado en type y EV > 0
                $verdict = '';
                if ($statisticType === 'single' && $evCalculation > 0) {
                    $verdict = 'Trending bet';
                } elseif ($statisticType === 'combined' && $evCalculation > 0) {
                    $verdict = 'Hot bet';
                }

                $valueBetsData->push([
                    'outcome'          => $statisticDetail->pretty_name,
                    'true_probability' => round($trueProbability, 4),
                    'market'           => $marketType,
                    'type'             => $statisticType,
                    'fair_odd'         => round($fairOdd, 2),
                    'api_odd'          => $apiOdd,
                    'ev_calculation'   => round($evCalculation, 4),
                    'verdict'          => $verdict,
                    // Campos de debug
                    'statistic_id'     => $statistic->id,
                    'statistic_detail_id' => $statisticDetail->id,
                    'config_pretty_name' => $statisticDetail->config_pretty_name,
                    'value'            => $statisticDetail->value,
                    'count'            => $statisticDetail->count,
                    'team_id'          => $statisticDetail->team_id,
                ]);
            }
        }

        // Debug info
        $debugInfo = [
            'fixture_id' => $fixture->id,
            'statistics_count' => $statistics->count(),
            'value_bets_generated' => $valueBetsData->count(),
            'statistics_sample' => $statistics->take(2)->map(function($stat) {
                return [
                    'id' => $stat->id,
                    'type' => $stat->type,
                    'market_id' => $stat->market_id,
                    'has_detail_1' => $stat->statisticDetailOne ? true : false,
                    'has_detail_2' => $stat->statisticDetailTwo ? true : false,
                ];
            }),
        ];

        $this->streaksService->handle([
            'homeTeamId' => $fixture->team_home,
            'awayTeamId' => $fixture->team_away,
        ]);

        $summary = [
            'total_value_bets'       => $valueBetsData->count(),
            'positive_ev_bets'       => $valueBetsData->where('ev_calculation', '>', 0)->count(),
            'trending_bets'          => $valueBetsData->where('verdict', 'Trending bet')->count(),
            'hot_bets'               => $valueBetsData->where('verdict', 'Hot bet')->count(),
        ];

        return response()->json([
            'fixture' => [
                'id'        => $fixture->id,
                'home_team' => $fixture->homeTeam?->name,
                'away_team' => $fixture->awayTeam?->name,
                'date'      => $fixture->date,
            ],
            'value_bets' => $valueBetsData->all(),
            'summary'    => $summary,
            'streaks'    => $this->streaksService->result,
            'debug'      => $debugInfo,
        ]);
    }
}