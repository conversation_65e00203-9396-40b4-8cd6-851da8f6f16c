<?php

namespace App\Http\Controllers\JogosHoje\Api\V3;

use App\Http\Controllers\Controller;
use App\Models\JogosHoje\Fixture;
use App\Services\JogosHoje\{ValueBetService, BetometerStreaksService};
use Illuminate\Http\{JsonResponse, Request};

class FixturesBetometerValueBetsController extends Controller
{
    public function __construct(private readonly ValueBetService $service, private readonly BetometerStreaksService $streaksService)
    {
    }

    public function __invoke(Request $request, Fixture $fixture): JsonResponse
    {
        // Get original odds as base (odds table)
        $models = collect($this->service->getValueBetsForFixture($fixture->id));

        // Get ALL statistics and statistic_details for this fixture as complement
        $statistics = \App\Models\JogosHoje\Statistic::where('fixture_id', $fixture->id)
            ->with(['statisticDetailOne', 'statisticDetailTwo'])
            ->get();

        $statisticDetails = \App\Models\JogosHoje\StatisticDetails::where('fixture_id', $fixture->id)->get();

        $valueBetsData = $models->map(function (\App\Models\Odd $odd) use ($fixture, $statistics, $statisticDetails) {
            // Find statistic_detail that corresponds to this market_id
            $relevantStatistic = $statistics->where('market_id', $odd->market_id)->first();

            // If no specific statistic for this market_id, search by config_pretty_name
            $statisticDetail = null;
            $statisticType = 'unknown';

            if ($relevantStatistic) {
                // Use the specific statistic for the market_id
                $statisticDetail = $relevantStatistic->statisticDetailOne ?: $relevantStatistic->statisticDetailTwo;
                $statisticType = $relevantStatistic->type ?? 'unknown';
            } else {
                // Search in all statistic_details by config_pretty_name = 'Vitórias' for 1x2
                if ($odd->market_id == 1) { // market_id 1 is 1x2
                    $statisticDetail = $statisticDetails->where('config_pretty_name', 'Vitórias')->first();
                    // Find the type from any statistic that uses this statistic_detail
                    if ($statisticDetail) {
                        $relatedStat = $statistics->where(function($stat) use ($statisticDetail) {
                            return $stat->statistic_details_1_id == $statisticDetail->id ||
                                   $stat->statistic_details_2_id == $statisticDetail->id;
                        })->first();
                        $statisticType = $relatedStat?->type ?? 'unknown';
                    }
                }
            }

            // If we don't find statistic_detail, use default values
            if (!$statisticDetail) {
                return [
                    'outcome'          => $odd->option_name,
                    'true_probability' => 0,
                    'market'           => $odd->market_id == 1 ? '1x2' : 'Other',
                    'type'             => 'unknown',
                    'fair_odd'         => 0,
                    'api_odd'          => 0,
                    'ev_calculation'   => -1,
                    'verdict'          => '',
                    // Debug
                    'market_id'        => $odd->market_id,
                    'bookmaker_id'     => $odd->bookmaker_id,
                    'original_odd'     => $odd->odd_value,
                    'debug_message'    => 'No statistic_detail found',
                ];
            }

            // True Probability: value/count from statistic_details
            $trueProbability = $statisticDetail->count > 0 ? $statisticDetail->value / $statisticDetail->count : 0.0;

            // Market: if config_pretty_name is "Vitórias" then it's "1x2"
            $marketType = $statisticDetail->config_pretty_name === 'Vitórias' ? '1x2' : 'Other';

            // Fair odd: 1 / true_probability
            $fairOdd = $trueProbability > 0 ? 1 / $trueProbability : 0.0;

            // API odd: choose the highest odd from "1x2" based on pretty_name
            $prettyName = $statisticDetail->pretty_name ?? '';
            $apiOdd = $prettyName ? $this->service->getMax1X2Odd($fixture, $prettyName) : 0.0;

            // EV Calculation: (true_probability * api_odd) - 1
            $evCalculation = ($trueProbability * $apiOdd) - 1;

            // Verdict: based on type and EV > 0
            $verdict = '';
            if ($statisticType === 'single' && $evCalculation > 0) {
                $verdict = 'Trending bet';
            } elseif ($statisticType === 'combined' && $evCalculation > 0) {
                $verdict = 'Hot bet';
            }

            return [
                'outcome'          => $odd->option_name,
                'true_probability' => round($trueProbability, 4),
                'market'           => $marketType,
                'type'             => $statisticType,
                'fair_odd'         => round($fairOdd, 2),
                'api_odd'          => $apiOdd,
                'ev_calculation'   => round($evCalculation, 4),
                'verdict'          => $verdict,
                // Debug
                'market_id'        => $odd->market_id,
                'bookmaker_id'     => $odd->bookmaker_id,
                'original_odd'     => $odd->odd_value,
                'config_pretty_name' => $statisticDetail->config_pretty_name,
                'pretty_name'      => $prettyName,
                'value'            => $statisticDetail->value,
                'count'            => $statisticDetail->count,
                'team_id'          => $statisticDetail->team_id,
            ];
        });

        // Debug info
        $debugInfo = [
            'fixture_id' => $fixture->id,
            'odds_count' => $models->count(),
            'statistics_count' => $statistics->count(),
            'statistic_details_count' => $statisticDetails->count(),
            'value_bets_generated' => $valueBetsData->count(),
        ];

        $this->streaksService->handle([
            'homeTeamId' => $fixture->team_home,
            'awayTeamId' => $fixture->team_away,
        ]);

        $summary = [
            'total_value_bets'       => $valueBetsData->count(),
            'positive_ev_bets'       => $valueBetsData->where('ev_calculation', '>', 0)->count(),
            'trending_bets'          => $valueBetsData->where('verdict', 'Trending bet')->count(),
            'hot_bets'               => $valueBetsData->where('verdict', 'Hot bet')->count(),
        ];

        return response()->json([
            'fixture' => [
                'id'        => $fixture->id,
                'home_team' => $fixture->homeTeam?->name,
                'away_team' => $fixture->awayTeam?->name,
                'date'      => $fixture->date,
            ],
            'value_bets' => $valueBetsData->all(),
            'summary'    => $summary,
            'streaks'    => $this->streaksService->result,
            'debug'      => $debugInfo,
        ]);
    }
}