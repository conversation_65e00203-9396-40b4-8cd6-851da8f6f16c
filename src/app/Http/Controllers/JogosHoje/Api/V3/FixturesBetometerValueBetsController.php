<?php

namespace App\Http\Controllers\JogosHoje\Api\V3;

use App\Http\Controllers\Controller;
use App\Models\JogosHoje\{Fixture, FixturePrediction};
use App\Services\JogosHoje\{ValueBetService, BetometerStreaksService};
use Illuminate\Http\{JsonResponse, Request};

class FixturesBetometerValueBetsController extends Controller
{
    public function __construct(private readonly ValueBetService $service, private readonly BetometerStreaksService $streaksService)
    {
    }

    public function __invoke(Request $request, Fixture $fixture): JsonResponse
    {
        // Get all statistics for this fixture (not filtered by market_id)
        $statisticsWithOdds = \Illuminate\Support\Facades\DB::select("
            SELECT
                s.*,
                sd.value,
                sd.count,
                sd.config_pretty_name,
                sd.pretty_name,
                sd.team_id,
                sd.config_type_id,
                sd.config_threshold
            FROM
                statistics s
            JOIN
                statistic_details sd ON sd.id = s.statistic_details_1_id
            WHERE
                s.fixture_id = ?
        ", [$fixture->id]);

        // Convert to collection for easier manipulation
        $statisticsCollection = collect($statisticsWithOdds);

        // Get all raw API odds for this fixture
        $rawOdds = $this->service->fetchRawOddsForFixture($fixture);

        // Process all available markets
        $allValueBets = collect();

        // Group statistics by market_id for processing
        $statisticsByMarket = $statisticsCollection->groupBy('market_id');

        // Process combined streaks first
        $combinedStreaks = $statisticsCollection->where('type', 'combined');
        foreach ($combinedStreaks as $combinedStreak) {
            $combinedValueBet = $this->processCombinedStreak($fixture, $combinedStreak, $rawOdds);
            if ($combinedValueBet) {
                $allValueBets->push($combinedValueBet);
            }
        }

        foreach ($statisticsByMarket as $marketId => $marketStatistics) {
            foreach ($marketStatistics as $statisticData) {
                // Skip combined streaks (already processed above)
                if ($statisticData->type === 'combined') {
                    continue;
                }

                // Filter based on new criteria for Goals, Corners, Cards
                if (!$this->shouldProcessStreak($statisticData)) {
                    continue;
                }



                // True Probability = value/count from statistic_detail table
                $value = $statisticData->value ?? 0;
                $count = $statisticData->count ?? 0;
                $trueProbability = $count > 0 ? $value / $count : 0.0;

                // Cap 100% streaks to 90% (fair_odd=1.10)
                if ($trueProbability >= 1.0) {
                    $trueProbability = 0.9;
                }

                // Adjust probability if it's 100% (5/5 or 7/7) to 90%
                if ($trueProbability >= 1.0) {
                    $trueProbability = 0.9; // 90%
                }

                // Fair odd = 1/true_probability (with protection against division by zero)
                $fairOdd = $trueProbability > 0 ? 1 / $trueProbability : 0.0;

                // Determine the recommended bet based on config_type_id
                $recommendedBet = $this->getRecommendedBet($fixture, $statisticData);

                // Get the API odd based on streak type
                $apiOdd = $this->getApiOddForStreak($fixture, $statisticData, $recommendedBet, $rawOdds);



                // If api_odd is 0, skip this bet
                if ($apiOdd == 0) {
                    if (str_contains(strtolower($statisticData->pretty_name ?? ''), 'total_goals')) {
                        \Log::info('Goals streak skipped - no API odd found');
                    }
                    continue;
                }

                // EV = (true_probability * api_odd) - 1
                $evCalculation = ($trueProbability * $apiOdd) - 1;



                // For UNDEFEATED/WINLESS, check if aggregated_predictions exists first
                $configTypeId = strtoupper($statisticData->config_type_id ?? '');
                if (($configTypeId === 'UNDEFEATED' || $configTypeId === 'WINLESS') && $marketId === 1) {
                    // Get prediction to check aggregated_predictions
                    $prediction = \App\Models\JogosHoje\FixturePrediction::where('fixture_id', $fixture->id)->first();
                    $prediction1x2 = $prediction ? $prediction->{'1x2'} : null;
                    $aggregatedPredictions = $prediction1x2['aggregated_predictions'] ?? null;

                    // If no aggregated_predictions, skip this bet
                    if (!$aggregatedPredictions) {
                        continue;
                    }
                }

                // Check if there's a prediction that supports this outcome
                $hasPredictionSupport = $this->checkPredictionSupportByStreakMarket($fixture, $marketId, $statisticData);

                // Verdict logic: 'single' + EV>0 = trending bet, 'combined' + EV>0 = hot bet
                // If has prediction support, upgrade to "Super Value bet"
                $verdict = '';
                if ($evCalculation > 0) {
                    if ($hasPredictionSupport) {
                        $verdict = 'Super Value bet';
                    } else {
                        $verdict = $statisticData->type === 'single' ? 'Trending bet' : 'Hot bet';
                    }
                }

                // Get outcome description with recommendation
                $outcome = $this->getOutcomeWithRecommendation($fixture, $statisticData, $recommendedBet);

                // Get market name based on streak type
                $marketName = $this->getMarketNameForStreak($statisticData, $marketId);

                $valueBet = [
                    'true_probability' => round($trueProbability, 4),
                    'market'           => $marketName,
                    'type'             => $statisticData->type ?? '',
                    'fair_odd'         => round($fairOdd, 2),
                    'api_odd'          => $apiOdd,
                    'ev_calculation'   => round($evCalculation, 4),
                    'verdict'          => $verdict,
                    'outcome'          => $outcome,
                    'has_prediction_support' => $hasPredictionSupport,
                    'market_id'        => $marketId,
                    'value'            => $value,
                    'count'            => $count,
                    'statistic_type'   => $statisticData->type ?? '',
                    'pretty_name'      => $statisticData->pretty_name ?? '',
                    'config_pretty_name' => $statisticData->config_pretty_name ?? '',
                    'config_type_id'   => $statisticData->config_type_id ?? '',
                    'config_threshold' => $statisticData->config_threshold ?? null,
                    'team_id'          => $statisticData->team_id ?? null,
                    'recommended_bet'  => $recommendedBet
                ];



                $allValueBets->push($valueBet);
            }
        }

        // Filter to show only positive EV bets
        $valueBetsData = $allValueBets->filter(function ($bet) {
            return $bet['ev_calculation'] > 0;
        });

        // Filter to show only the highest api_odd bet per market and team combination
        $filteredValueBets = $valueBetsData->groupBy(function ($bet) {
                return $bet['market_id'] . '_' . ($bet['team_id'] ?? 'no_team');
            })
            ->map(function ($marketBets) {
                return $marketBets->sortByDesc('api_odd')->first();
            })
            ->values();

        // Debug info
        $debugInfo = [
            'fixture_id' => $fixture->id,
            'statistics_with_odds_count' => $statisticsCollection->count(),
            'value_bets_generated' => $valueBetsData->count(),
            'filtered_value_bets' => $filteredValueBets->count(),
            'statistics_sample' => $statisticsCollection->take(10)->map(function($stat) {
                return [
                    'market_id' => $stat->market_id,
                    'type' => $stat->type,
                    'value' => $stat->value,
                    'count' => $stat->count,
                    'config_type_id' => $stat->config_type_id ?? 'NULL',
                    'pretty_name' => $stat->pretty_name ?? 'NULL',
                    'config_threshold' => $stat->config_threshold ?? 'NULL',
                    'should_process' => $this->shouldProcessStreak($stat) ? 'YES' : 'NO',
                ];
            }),
            'winless_debug' => $this->getWinlessDebugInfo($fixture, $statisticsCollection),
            'combined_streaks_debug' => $combinedStreaks->map(function($streak) {
                return [
                    'pretty_name' => $streak->pretty_name,
                    'type' => $streak->type,
                    'has_pipe' => str_contains($streak->pretty_name ?? '', '|'),
                ];
            }),
        ];

        // Get streaks specific to this fixture using your query
        $fixtureStreaks = \Illuminate\Support\Facades\DB::select("
            SELECT s.*, sd.value, sd.count
            FROM statistics s
            JOIN statistic_details sd ON sd.id = s.statistic_details_1_id
            WHERE s.fixture_id = ?
        ", [$fixture->id]);

        $summary = [
            'total_value_bets_found'   => $valueBetsData->count(),
            'highest_api_odd_shown'    => $filteredValueBets->count(),
            'positive_ev_bets'         => $valueBetsData->where('ev_calculation', '>', 0)->count(),
            'trending_bets'            => $valueBetsData->where('verdict', 'Trending bet')->count(),
            'hot_bets'                 => $valueBetsData->where('verdict', 'Hot bet')->count(),
            'super_value_bets'         => $valueBetsData->where('verdict', 'Super Value bet')->count(),
            'bets_with_prediction_support' => $valueBetsData->where('has_prediction_support', true)->count(),
        ];

        // Get predictions for this fixture
        $predictions = FixturePrediction::where('fixture_id', $fixture->id)->first();

        return response()->json([
            'fixture' => [
                'id'        => $fixture->id,
                'home_team' => $fixture->homeTeam?->name,
                'away_team' => $fixture->awayTeam?->name,
                'date'      => $fixture->date,
            ],
            'value_bets' => $filteredValueBets->all(),
            'predictions' => $predictions,
            'raw_api_odds' => $rawOdds,
            'summary'    => $summary,
            'streaks'    => $fixtureStreaks,
            'debug'      => $debugInfo,
        ]);
    }

    /**
     * Check if streak should be processed based on new criteria
     */
    private function shouldProcessStreak($statisticData): bool
    {
        $prettyName = strtolower($statisticData->pretty_name ?? '');
        $configThreshold = $statisticData->config_threshold ?? null;
        $configTypeId = strtoupper($statisticData->config_type_id ?? '');

        // WINLESS/UNDEFEATED (already implemented)
        if ($configTypeId === 'WINLESS' || $configTypeId === 'UNDEFEATED' || $configTypeId === 'DEFEATS' || $configTypeId === 'WINS' || $configTypeId === 'DRAWS') {
            return true;
        }

        // Corners: only 10.5 threshold
        if ((str_contains($prettyName, 'corners') ||
             str_contains($prettyName, 'corners_conceded') ||
             str_contains($prettyName, 'total_corners')) &&
            $configThreshold == 10.5) {
            return true;
        }

        // Goals: only 2.5 threshold
        if (str_contains($prettyName, 'goal') && $configThreshold == 2.5) {
            return true;
        }

        // Cards: only 4.5 threshold
        if ((str_contains($prettyName, 'card') ||
             str_contains($prettyName, 'total_cards') ||
             str_contains($prettyName, 'cards_conceded')) &&
            $configThreshold == 4.5) {
            return true;
        }

        return false;
    }

    /**
     * Process combined streaks
     */
    private function processCombinedStreak(Fixture $fixture, $combinedStreak, $rawOdds): ?array
    {
        $prettyName = $combinedStreak->pretty_name ?? '';

        // Parse combined streak format: "EQUIPA A (6/7 wins) | EQUIPA B (7/7 winless)"
        if (!str_contains($prettyName, '|')) {
            return null; // Not a valid combined streak
        }

        $parts = explode('|', $prettyName);
        if (count($parts) !== 2) {
            return null;
        }

        $teamA = trim($parts[0]);
        $teamB = trim($parts[1]);

        // Extract streak types
        $teamAType = $this->extractStreakType($teamA);
        $teamBType = $this->extractStreakType($teamB);

        // Determine bet recommendation based on combination
        $recommendation = $this->getCombinedStreakRecommendation($fixture, $teamA, $teamB, $teamAType, $teamBType);

        if (!$recommendation) {
            return null; // No valid recommendation
        }

        // Calculate probability and odds
        $value = $combinedStreak->value ?? 0;
        $count = $combinedStreak->count ?? 1;
        $trueProbability = $count > 0 ? $value / $count : 0;

        // Cap 100% streaks to 90%
        if ($trueProbability >= 1.0) {
            $trueProbability = 0.9;
        }

        $fairOdd = $trueProbability > 0 ? 1 / $trueProbability : 0.0;

        // Get API odd based on recommendation
        $apiOdd = $this->getApiOddForCombinedStreak($rawOdds, $recommendation);

        if ($apiOdd == 0) {
            return null;
        }

        $evCalculation = ($trueProbability * $apiOdd) - 1;

        if ($evCalculation <= 0) {
            return null; // Only positive EV
        }

        // Check prediction support
        $hasPredictionSupport = $this->checkCombinedStreakPredictionSupport($fixture, $recommendation);

        $verdict = $hasPredictionSupport ? 'Super Value bet' : 'Hot bet';

        return [
            'true_probability' => round($trueProbability, 4),
            'market' => $recommendation['market'] ?? 'Combined',
            'type' => 'combined',
            'fair_odd' => round($fairOdd, 2),
            'api_odd' => $apiOdd,
            'ev_calculation' => round($evCalculation, 4),
            'verdict' => $verdict,
            'outcome' => $prettyName . ' → ' . ($recommendation['description'] ?? ''),
            'has_prediction_support' => $hasPredictionSupport,
            'market_id' => $recommendation['market_id'] ?? 0,
            'value' => $value,
            'count' => $count,
            'statistic_type' => 'combined',
            'pretty_name' => $prettyName,
            'config_pretty_name' => $combinedStreak->config_pretty_name ?? '',
            'config_type_id' => 'combined',
            'config_threshold' => null,
            'team_id' => null,
            'recommended_bet' => $recommendation
        ];
    }

    /**
     * Extract streak type from team description
     */
    private function extractStreakType(string $teamDescription): string
    {
        if (str_contains($teamDescription, 'wins)')) return 'wins';
        if (str_contains($teamDescription, 'winless)')) return 'winless';
        if (str_contains($teamDescription, 'defeats)')) return 'defeats';
        if (str_contains($teamDescription, 'undefeated)')) return 'undefeated';
        return 'unknown';
    }

    /**
     * Get combined streak recommendation
     */
    private function getCombinedStreakRecommendation(Fixture $fixture, string $teamA, string $teamB, string $typeA, string $typeB): ?array
    {
        // Load team relationships
        $fixture->load(['homeTeam', 'awayTeam']);

        // EQUIPA A (6/7 wins) | EQUIPA B (7/7 winless) = EQUIPA A chance dupla WIN/draw
        if ($typeA === 'wins' && $typeB === 'winless') {
            $teamAName = $this->extractTeamName($teamA);
            return [
                'type' => 'double_chance',
                'market_id' => 12,
                'market' => 'Double Chance',
                'description' => "Apostar em {$teamAName} ganha ou empate",
                'double_chance_type' => $this->getDoubleChanceTypeForTeam($fixture, $teamAName)
            ];
        }

        // EQUIPA A (5/5 defeats) | EQUIPA B (4/5 wins) = EQUIPA B WIN, 1x2
        if ($typeA === 'defeats' && $typeB === 'wins') {
            $teamBName = $this->extractTeamName($teamB);
            return [
                'type' => '1x2_win',
                'market_id' => 1,
                'market' => '1X2',
                'description' => "Apostar na vitória do {$teamBName}"
            ];
        }

        // EQUIPA A (5/5 defeats) | EQUIPA B (4/5 undefeated) = Equipa B chance dupla Win/draw
        if ($typeA === 'defeats' && $typeB === 'undefeated') {
            $teamBName = $this->extractTeamName($teamB);
            return [
                'type' => 'double_chance',
                'market_id' => 12,
                'market' => 'Double Chance',
                'description' => "Apostar em {$teamBName} ganha ou empate",
                'double_chance_type' => $this->getDoubleChanceTypeForTeam($fixture, $teamBName)
            ];
        }

        // EQUIPA A (2/3 wins) | EQUIPA B (1/1 wins) = não damos value bet (contrárias)
        if ($typeA === 'wins' && $typeB === 'wins') {
            return null;
        }

        // EQUIPA A (1/1 defeats) | EQUIPA B (1/1 defeats) = não damos value bet (contrárias)
        if ($typeA === 'defeats' && $typeB === 'defeats') {
            return null;
        }

        // EQUIPA A (22/34 winless) | EQUIPA B (24/34 winless) = só se houver prediction de empate
        if ($typeA === 'winless' && $typeB === 'winless') {
            return [
                'type' => 'draw_prediction',
                'market_id' => 1,
                'market' => '1X2',
                'description' => "Apostar no empate (ambas winless)",
                'requires_draw_prediction' => true
            ];
        }

        // EQUIPA A (1/1 undefeated) | EQUIPA B (1/1 undefeated) = só se houver prediction de empate
        if ($typeA === 'undefeated' && $typeB === 'undefeated') {
            return [
                'type' => 'draw_prediction',
                'market_id' => 1,
                'market' => '1X2',
                'description' => "Apostar no empate (ambas undefeated)",
                'requires_draw_prediction' => true
            ];
        }

        return null;
    }

    /**
     * Extract team name from description
     */
    private function extractTeamName(string $teamDescription): string
    {
        // Extract team name before the first parenthesis
        $pos = strpos($teamDescription, '(');
        if ($pos !== false) {
            return trim(substr($teamDescription, 0, $pos));
        }
        return trim($teamDescription);
    }

    /**
     * Get double chance type for team
     */
    private function getDoubleChanceTypeForTeam(Fixture $fixture, string $teamName): string
    {
        if ($fixture->homeTeam && str_contains(strtolower($teamName), strtolower($fixture->homeTeam->name))) {
            return '1x'; // Home/Draw
        }
        return 'x2'; // Draw/Away
    }

    /**
     * Get API odd for combined streak
     */
    private function getApiOddForCombinedStreak($rawOdds, array $recommendation): float
    {
        $type = $recommendation['type'] ?? '';
        $marketId = $recommendation['market_id'] ?? 0;

        if ($type === 'double_chance') {
            return $this->getDoubleChanceOdd($rawOdds, $recommendation);
        }

        if ($type === '1x2_win' || $type === 'draw_prediction') {
            return $this->get1X2Odd($rawOdds, $recommendation);
        }

        return 0.0;
    }

    /**
     * Get 1X2 odd for team-specific bets (WINS/DEFEATS/DRAWS)
     */
    private function get1X2OddForTeam($rawOdds, array $recommendedBet, Fixture $fixture): float
    {
        $type = $recommendedBet['type'] ?? '';
        $maxOdd = 0.0;
        $targetValue = '';

        // Determine target value based on bet type
        switch ($type) {
            case 'home_win':
                $targetValue = 'Home';
                break;
            case 'away_win':
                $targetValue = 'Away';
                break;
            case 'draw':
                $targetValue = 'Draw';
                break;
            default:
                return 0.0;
        }

        foreach ($rawOdds as $entry) {
            if (empty($entry->bookmakers)) continue;

            foreach ($entry->bookmakers as $bookmaker) {
                if (empty($bookmaker->bets)) continue;

                foreach ($bookmaker->bets as $bet) {
                    if ($bet->id == 1 && !empty($bet->values)) { // 1X2 market
                        foreach ($bet->values as $value) {
                            if ($value->value === $targetValue) {
                                $odd = (float) $value->odd;
                                $maxOdd = max($maxOdd, $odd);
                            }
                        }
                    }
                }
            }
        }

        return $maxOdd;
    }

    /**
     * Get 1X2 odd from raw odds (for combined streaks)
     */
    private function get1X2Odd($rawOdds, array $recommendation): float
    {
        $maxOdd = 0.0;
        $targetValue = '';

        if ($recommendation['type'] === 'draw_prediction') {
            $targetValue = 'Draw';
        } else {
            // For team wins, we'd need to determine Home/Away
            $targetValue = 'Home'; // Simplified for now
        }

        foreach ($rawOdds as $entry) {
            if (empty($entry->bookmakers)) continue;

            foreach ($entry->bookmakers as $bookmaker) {
                if (empty($bookmaker->bets)) continue;

                foreach ($bookmaker->bets as $bet) {
                    if ($bet->id == 1 && !empty($bet->values)) { // 1X2 market
                        foreach ($bet->values as $value) {
                            if ($value->value === $targetValue) {
                                $odd = (float) $value->odd;
                                $maxOdd = max($maxOdd, $odd);
                            }
                        }
                    }
                }
            }
        }

        return $maxOdd;
    }

    /**
     * Check combined streak prediction support
     */
    private function checkCombinedStreakPredictionSupport(Fixture $fixture, array $recommendation): bool
    {
        $prediction = FixturePrediction::where('fixture_id', $fixture->id)->first();

        if (!$prediction) {
            return false;
        }

        $prediction1x2 = $prediction->{'1x2'};

        if (!$prediction1x2 || !isset($prediction1x2['status']) || $prediction1x2['status'] !== 'available') {
            return false;
        }

        // For draw predictions, check if draw has highest probability
        if ($recommendation['requires_draw_prediction'] ?? false) {
            $homeProbability = $prediction1x2['home_probability'] ?? 0;
            $drawProbability = $prediction1x2['draw_probability'] ?? 0;
            $awayProbability = $prediction1x2['away_probability'] ?? 0;

            $maxProbability = max($homeProbability, $drawProbability, $awayProbability);
            return $maxProbability == $drawProbability;
        }

        // For double chance, check aggregated predictions
        if ($recommendation['type'] === 'double_chance') {
            $aggregatedPredictions = $prediction1x2['aggregated_predictions'] ?? null;
            if (!$aggregatedPredictions) {
                return false;
            }

            $doubleChanceType = $recommendation['double_chance_type'] ?? '';
            if ($doubleChanceType === '1x' && isset($aggregatedPredictions['1x'])) {
                return $aggregatedPredictions['1x'] > 0.5;
            } elseif ($doubleChanceType === 'x2' && isset($aggregatedPredictions['x2'])) {
                return $aggregatedPredictions['x2'] > 0.5;
            }
        }

        return true; // Default to true for other cases
    }

    /**
     * Get recommended bet based on config_type_id
     */
    private function getRecommendedBet(Fixture $fixture, $statisticData): array
    {
        $configTypeId = $statisticData->config_type_id ?? '';
        $teamId = $statisticData->team_id ?? null;

        // Load team relationships
        $fixture->load(['homeTeam', 'awayTeam']);

        $recommendation = [
            'type' => '',
            'team_id' => null,
            'team_name' => '',
            'description' => ''
        ];

        switch (strtoupper($configTypeId)) {
            case 'DEFEATS':
                // Apostar na equipa contrária (vitória simples 1X2)
                if ($teamId == $fixture->homeTeam->id) {
                    $recommendation = [
                        'type' => 'away_win',
                        'market_id' => 1,
                        'team_id' => $fixture->awayTeam->id,
                        'team_name' => $fixture->awayTeam->name,
                        'description' => "Apostar na vitória do {$fixture->awayTeam->name}"
                    ];
                } else {
                    $recommendation = [
                        'type' => 'home_win',
                        'market_id' => 1,
                        'team_id' => $fixture->homeTeam->id,
                        'team_name' => $fixture->homeTeam->name,
                        'description' => "Apostar na vitória do {$fixture->homeTeam->name}"
                    ];
                }
                break;

            case 'WINLESS':
                // Chance dupla: outra equipa ganha ou empate
                if ($teamId == $fixture->homeTeam->id) {
                    // Winless team é home, apostar em away/draw (x2)
                    $recommendation = [
                        'type' => 'double_chance_x2',
                        'team_id' => $fixture->awayTeam->id,
                        'team_name' => $fixture->awayTeam->name,
                        'description' => "Apostar em {$fixture->awayTeam->name} ganha ou empate",
                        'double_chance_type' => 'x2'
                    ];
                } else {
                    // Winless team é away, apostar em home/draw (1x)
                    $recommendation = [
                        'type' => 'double_chance_1x',
                        'team_id' => $fixture->homeTeam->id,
                        'team_name' => $fixture->homeTeam->name,
                        'description' => "Apostar em {$fixture->homeTeam->name} ganha ou empate",
                        'double_chance_type' => '1x'
                    ];
                }
                break;

            case 'UNDEFEATED':
                // Chance dupla: mesma equipa ganha ou empate
                if ($teamId == $fixture->homeTeam->id) {
                    // Undefeated team é home, apostar em home/draw (1x)
                    $recommendation = [
                        'type' => 'double_chance_1x',
                        'team_id' => $fixture->homeTeam->id,
                        'team_name' => $fixture->homeTeam->name,
                        'description' => "Apostar em {$fixture->homeTeam->name} ganha ou empate",
                        'double_chance_type' => '1x'
                    ];
                } else {
                    // Undefeated team é away, apostar em away/draw (x2)
                    $recommendation = [
                        'type' => 'double_chance_x2',
                        'team_id' => $fixture->awayTeam->id,
                        'team_name' => $fixture->awayTeam->name,
                        'description' => "Apostar em {$fixture->awayTeam->name} ganha ou empate",
                        'double_chance_type' => 'x2'
                    ];
                }
                break;

            case 'WINS':
                // Apostar na mesma equipa (vitória simples 1X2)
                if ($teamId == $fixture->homeTeam->id) {
                    $recommendation = [
                        'type' => 'home_win',
                        'market_id' => 1,
                        'team_id' => $fixture->homeTeam->id,
                        'team_name' => $fixture->homeTeam->name,
                        'description' => "Apostar na vitória do {$fixture->homeTeam->name}"
                    ];
                } else {
                    $recommendation = [
                        'type' => 'away_win',
                        'market_id' => 1,
                        'team_id' => $fixture->awayTeam->id,
                        'team_name' => $fixture->awayTeam->name,
                        'description' => "Apostar na vitória do {$fixture->awayTeam->name}"
                    ];
                }
                break;

            case 'DRAWS':
                // Apostar no empate (1X2)
                $recommendation = [
                    'type' => 'draw',
                    'market_id' => 1,
                    'team_id' => null,
                    'team_name' => 'Empate',
                    'description' => "Apostar no empate"
                ];
                break;

            default:
                // Para Goals, Corners, Cards - usar Over/Under
                return $this->getOverUnderRecommendation($fixture, $statisticData);
        }

        return $recommendation;
    }

    /**
     * Get Over/Under recommendation for Goals/Corners/Cards
     */
    private function getOverUnderRecommendation(Fixture $fixture, $statisticData): array
    {
        $prettyName = strtolower($statisticData->pretty_name ?? '');
        $configThreshold = $statisticData->config_threshold ?? null;
        $teamId = $statisticData->team_id ?? null;

        // Determine Over/Under based on < or > in pretty_name
        $isUnder = str_contains($prettyName, '<');
        $isOver = str_contains($prettyName, '>');

        if (!$isUnder && !$isOver) {
            return []; // No clear direction
        }

        $direction = $isUnder ? 'Under' : 'Over';

        // Format threshold to remove unnecessary .00
        $formattedThreshold = rtrim(rtrim($configThreshold, '0'), '.');

        // Determine market based on content
        if (str_contains($prettyName, 'goal')) {
            return [
                'type' => 'goals_over_under',
                'market_id' => 5,
                'value' => "{$direction} {$formattedThreshold}",
                'description' => "Apostar {$direction} {$formattedThreshold} goals",
                'direction' => strtolower($direction),
                'threshold' => $configThreshold
            ];
        }

        if (str_contains($prettyName, 'corners') || str_contains($prettyName, 'total_corners')) {
            return [
                'type' => 'corners_over_under',
                'market_id' => 45,
                'value' => "{$direction} {$formattedThreshold}",
                'description' => "Apostar {$direction} {$formattedThreshold} corners",
                'direction' => strtolower($direction),
                'threshold' => $configThreshold
            ];
        }

        if (str_contains($prettyName, 'card') || str_contains($prettyName, 'total_cards')) {
            // Determine if it's team-specific or total
            if (str_contains($prettyName, 'total_cards')) {
                // Team-specific cards
                $isHomeTeam = $teamId == $fixture->homeTeam->id;
                $marketId = $isHomeTeam ? 82 : 83;
                $teamName = $isHomeTeam ? $fixture->homeTeam->name : $fixture->awayTeam->name;

                return [
                    'type' => 'team_cards_over_under',
                    'market_id' => $marketId,
                    'value' => "{$direction} {$formattedThreshold}",
                    'description' => "Apostar {$direction} {$formattedThreshold} cards para {$teamName}",
                    'direction' => strtolower($direction),
                    'threshold' => $configThreshold,
                    'team_specific' => true
                ];
            } else {
                // Total cards
                return [
                    'type' => 'cards_over_under',
                    'market_id' => 80,
                    'value' => "{$direction} {$formattedThreshold}",
                    'description' => "Apostar {$direction} {$formattedThreshold} cards total",
                    'direction' => strtolower($direction),
                    'threshold' => $configThreshold
                ];
            }
        }

        return [];
    }

    /**
     * Get API odd for streak based on type
     */
    private function getApiOddForStreak(Fixture $fixture, $statisticData, array $recommendedBet, $rawOdds): float
    {
        $configTypeId = strtoupper($statisticData->config_type_id ?? '');

        // For WINLESS/UNDEFEATED, use Double Chance odds
        if ($configTypeId === 'WINLESS' || $configTypeId === 'UNDEFEATED') {
            return $this->getDoubleChanceOdd($rawOdds, $recommendedBet);
        }

        // For WINS/DEFEATS/DRAWS, use 1X2 odds
        if ($configTypeId === 'WINS' || $configTypeId === 'DEFEATS' || $configTypeId === 'DRAWS') {
            return $this->get1X2OddForTeam($rawOdds, $recommendedBet, $fixture);
        }

        // For Goals/Corners/Cards, use Over/Under odds
        return $this->getOverUnderOdd($rawOdds, $recommendedBet);
    }

    /**
     * Get Over/Under odd from raw odds
     */
    private function getOverUnderOdd($rawOdds, array $recommendedBet): float
    {
        $targetMarketId = $recommendedBet['market_id'] ?? 0;
        $targetValue = $recommendedBet['value'] ?? '';
        $maxOdd = 0.0;

        foreach ($rawOdds as $entry) {
            if (empty($entry->bookmakers)) continue;

            foreach ($entry->bookmakers as $bookmaker) {
                if (empty($bookmaker->bets)) continue;

                foreach ($bookmaker->bets as $bet) {
                    if ($bet->id == $targetMarketId && !empty($bet->values)) {
                        foreach ($bet->values as $value) {
                            if ($value->value === $targetValue) {
                                $odd = (float) $value->odd;
                                $maxOdd = max($maxOdd, $odd);
                            }
                        }
                    }
                }
            }
        }

        return $maxOdd;
    }

    /**
     * Get market name for streak
     */
    private function getMarketNameForStreak($statisticData, $marketId): string
    {
        $configTypeId = strtoupper($statisticData->config_type_id ?? '');
        $prettyName = strtolower($statisticData->pretty_name ?? '');

        // 1X2 for WINS/DEFEATS/DRAWS
        if ($configTypeId === 'WINS' || $configTypeId === 'DEFEATS' || $configTypeId === 'DRAWS') {
            return '1X2';
        }

        // Double Chance for WINLESS/UNDEFEATED
        if ($configTypeId === 'WINLESS' || $configTypeId === 'UNDEFEATED') {
            return 'Double Chance';
        }

        // Over/Under markets
        if (str_contains($prettyName, 'goal')) {
            return 'Goals Over/Under';
        }

        if (str_contains($prettyName, 'corners')) {
            return 'Corners Over Under';
        }

        if (str_contains($prettyName, 'card')) {
            if (str_contains($prettyName, 'total_cards')) {
                return 'Team Total Cards';
            }
            return 'Cards Over/Under';
        }

        return $this->service->getMarketName($marketId);
    }

    /**
     * Check if there's a prediction that supports this outcome based on streak market_id
     */
    private function checkPredictionSupportByStreakMarket(Fixture $fixture, int $streakMarketId, $statisticData): bool
    {
        // Get prediction for this fixture
        $prediction = FixturePrediction::where('fixture_id', $fixture->id)->first();

        if (!$prediction) {
            return false;
        }

        // Get recommended bet for this streak
        $recommendedBet = $this->getRecommendedBet($fixture, $statisticData);

        // Map streak market_id to prediction type with new validation logic
        switch ($streakMarketId) {
            case 1: // Streak market_id 1 -> 1X2 predictions
                return $this->check1X2PredictionSupportWithRecommendation($prediction, $fixture, $statisticData, $recommendedBet);

            case 2: // Streak market_id 2 -> Cards predictions
                return $this->checkCardsPredictionSupportWithThreshold($prediction, $statisticData);

            case 3: // Streak market_id 3 -> Corners predictions
                return $this->checkCornersPredictionSupportWithThreshold($prediction, $statisticData);

            case 4: // Streak market_id 4 -> Goals predictions
                return $this->checkGoalsPredictionSupportWithThreshold($prediction, $statisticData);

            default:
                return false;
        }
    }

    /**
     * Check if there's a prediction that supports this outcome (OLD METHOD - keeping for reference)
     */
    private function checkPredictionSupport(Fixture $fixture, int $marketId, $statisticData): bool
    {
        // Get prediction for this fixture
        $prediction = FixturePrediction::where('fixture_id', $fixture->id)->first();

        if (!$prediction) {
            return false;
        }

        $prettyName = $statisticData->pretty_name ?? '';
        $configPrettyName = $statisticData->config_pretty_name ?? '';

        // Load team relationships
        $fixture->load(['homeTeam', 'awayTeam']);

        // Map market API IDs to their corresponding predictions
        // Based on the mapping you provided:
        switch ($marketId) {
            case 1:  // 1X2 WIN -> PREDICTIONS 1X2
                return $this->check1X2PredictionSupport($prediction, $fixture, $prettyName);

            case 3:  // 1ST HALF RESULT -> PREDICTIONS 1X2
                return $this->check1X2PredictionSupport($prediction, $fixture, $prettyName);

            case 5:  // TOTAL GOALS O/U -> PREDICTIONS GOALS
                return $this->checkGoalsPredictionSupport($prediction, $configPrettyName);

            case 8:  // BTTS (YES/NO) -> PREDICTIONS GOALS
                return $this->checkGoalsPredictionSupport($prediction, $configPrettyName);

            case 14: // 1ST TO SCORE -> PREDICTIONS GOALS
                return $this->checkGoalsPredictionSupport($prediction, $configPrettyName);

            case 45: // TOTAL CORNERS O/U -> PREDICTIONS CORNERS
                return $this->checkCornersPredictionSupport($prediction);

            case 80: // CARD O/U -> PREDICTIONS CARDS
                return $this->checkCardsPredictionSupport($prediction);

            default:
                return false;
        }
    }

    /**
     * Check 1X2 prediction support with new recommendation logic
     */
    private function check1X2PredictionSupportWithRecommendation(FixturePrediction $prediction, Fixture $fixture, $statisticData, array $recommendedBet): bool
    {
        $prediction1x2 = $prediction->{'1x2'};

        if (!$prediction1x2 || !isset($prediction1x2['status']) || $prediction1x2['status'] !== 'available') {
            return false;
        }

        $configTypeId = strtoupper($statisticData->config_type_id ?? '');

        // For UNDEFEATED and WINLESS, check aggregated_predictions
        if ($configTypeId === 'UNDEFEATED' || $configTypeId === 'WINLESS') {
            $aggregatedPredictions = $prediction1x2['aggregated_predictions'] ?? null;

            // If no aggregated_predictions, don't count as value bet
            if (!$aggregatedPredictions) {
                return false;
            }

            $doubleChanceType = $recommendedBet['double_chance_type'] ?? '';

            // Check if the aggregated prediction supports our recommendation
            if ($doubleChanceType === '1x' && isset($aggregatedPredictions['1x'])) {
                return $aggregatedPredictions['1x'] > 0.5; // Arbitrary threshold
            } elseif ($doubleChanceType === 'x2' && isset($aggregatedPredictions['x2'])) {
                return $aggregatedPredictions['x2'] > 0.5; // Arbitrary threshold
            }

            return false;
        }

        // For other types (DEFEATS, WINS, DRAWS), use original logic
        $homeProbability = $prediction1x2['home_probability'] ?? 0;
        $drawProbability = $prediction1x2['draw_probability'] ?? 0;
        $awayProbability = $prediction1x2['away_probability'] ?? 0;

        // Find which outcome has the highest probability
        $maxProbability = max($homeProbability, $drawProbability, $awayProbability);
        $predictedOutcome = '';

        if ($maxProbability == $homeProbability) {
            $predictedOutcome = 'home_win';
        } elseif ($maxProbability == $drawProbability) {
            $predictedOutcome = 'draw';
        } else {
            $predictedOutcome = 'away_win';
        }

        // Check if prediction matches our recommendation
        return $predictedOutcome === $recommendedBet['type'];
    }

    /**
     * OLD METHOD - keeping for reference
     */
    private function check1X2PredictionSupport(FixturePrediction $prediction, Fixture $fixture, string $prettyName): bool
    {
        $prediction1x2 = $prediction->{'1x2'};

        if (!$prediction1x2 || !isset($prediction1x2['status'])) {
            return false;
        }

        // Simply check if status is "available"
        return $prediction1x2['status'] === 'available';
    }

    /**
     * Check Goals prediction support with threshold validation
     */
    private function checkGoalsPredictionSupportWithThreshold(FixturePrediction $prediction, $statisticData): bool
    {
        $goalsData = $prediction->goals;

        if (!$goalsData || !isset($goalsData['status']) || $goalsData['status'] !== 'available') {
            return false;
        }

        $configThreshold = $statisticData->config_threshold ?? null;

        // Only validate if we have a threshold of 2.5
        if ($configThreshold == 2.5) {
            $probability = $goalsData['probability'] ?? 0;

            // If probability < 0.5 = under 2.5, if > 0.5 = over 2.5
            // This validation logic can be expanded based on streak type
            return true; // For now, just check if available
        }

        return $goalsData['status'] === 'available';
    }

    /**
     * Check Corners prediction support with threshold validation
     */
    private function checkCornersPredictionSupportWithThreshold(FixturePrediction $prediction, $statisticData): bool
    {
        $cornersData = $prediction->corners;

        if (!$cornersData || !isset($cornersData['status']) || $cornersData['status'] !== 'available') {
            return false;
        }

        $configThreshold = $statisticData->config_threshold ?? null;

        // Only validate if we have a threshold of 10.5
        if ($configThreshold == 10.5) {
            $probability = $cornersData['probability'] ?? 0;

            // If probability < 0.5 = under 10.5, if > 0.5 = over 10.5
            // This validation logic can be expanded based on streak type
            return true; // For now, just check if available
        }

        return $cornersData['status'] === 'available';
    }

    /**
     * Check Cards prediction support with threshold validation
     */
    private function checkCardsPredictionSupportWithThreshold(FixturePrediction $prediction, $statisticData): bool
    {
        $cardsData = $prediction->cards;

        if (!$cardsData || !isset($cardsData['status']) || $cardsData['status'] !== 'available') {
            return false;
        }

        $configThreshold = $statisticData->config_threshold ?? null;

        // Only validate if we have a threshold of 4.5
        if ($configThreshold == 4.5) {
            $probability = $cardsData['probability'] ?? 0;

            // If probability < 0.5 = under 4.5, if > 0.5 = over 4.5
            // This validation logic can be expanded based on streak type
            return true; // For now, just check if available
        }

        return $cardsData['status'] === 'available';
    }

    /**
     * OLD METHODS - keeping for reference
     */
    private function checkGoalsPredictionSupport(FixturePrediction $prediction, string $configPrettyName): bool
    {
        $goalsData = $prediction->goals;

        if (!$goalsData || !isset($goalsData['status'])) {
            return false;
        }

        // Simply check if status is "available"
        return $goalsData['status'] === 'available';
    }

    private function checkCornersPredictionSupport(FixturePrediction $prediction): bool
    {
        $cornersData = $prediction->corners;

        if (!$cornersData || !isset($cornersData['status'])) {
            return false;
        }

        // Simply check if status is "available"
        return $cornersData['status'] === 'available';
    }

    private function checkCardsPredictionSupport(FixturePrediction $prediction): bool
    {
        $cardsData = $prediction->cards;

        if (!$cardsData || !isset($cardsData['status'])) {
            return false;
        }

        // Simply check if status is "available"
        return $cardsData['status'] === 'available';
    }

    /**
     * Debug info for WINLESS streaks
     */
    private function getWinlessDebugInfo(Fixture $fixture, $statisticsCollection): array
    {
        $winlessStreaks = $statisticsCollection->filter(function($stat) {
            return $stat->market_id == 1 && strtoupper($stat->config_type_id ?? '') === 'WINLESS';
        });

        if ($winlessStreaks->isEmpty()) {
            return ['message' => 'No WINLESS streaks found'];
        }

        $winlessStreak = $winlessStreaks->first();
        $recommendedBet = $this->getRecommendedBet($fixture, $winlessStreak);
        $doubleChanceOdd = $this->getDoubleChanceOdd($fixture, $recommendedBet);

        // Get prediction
        $prediction = \App\Models\JogosHoje\FixturePrediction::where('fixture_id', $fixture->id)->first();
        $prediction1x2 = $prediction ? $prediction->{'1x2'} : null;
        $aggregatedPredictions = $prediction1x2['aggregated_predictions'] ?? null;

        $trueProbability = $winlessStreak->value / $winlessStreak->count;

        // Cap 100% streaks to 90% (fair_odd=1.10)
        if ($trueProbability >= 1.0) {
            $trueProbability = 0.9;
        }

        $evCalculation = ($trueProbability * $doubleChanceOdd) - 1;

        return [
            'streak' => [
                'pretty_name' => $winlessStreak->pretty_name,
                'config_type_id' => $winlessStreak->config_type_id,
                'value' => $winlessStreak->value,
                'count' => $winlessStreak->count,
                'true_probability' => $trueProbability,
            ],
            'recommended_bet' => $recommendedBet,
            'double_chance_odd' => $doubleChanceOdd,
            'aggregated_predictions' => $aggregatedPredictions,
            'ev_calculation' => $evCalculation,
            'would_appear' => $aggregatedPredictions && $evCalculation > 0,
        ];
    }

    /**
     * Get Double Chance odd from raw odds
     */
    private function getDoubleChanceOdd($rawOdds, array $recommendedBet): float
    {
        $doubleChanceType = $recommendedBet['double_chance_type'] ?? '';
        $maxOdd = 0.0;

        foreach ($rawOdds as $entry) {
            if (empty($entry->bookmakers)) continue;

            foreach ($entry->bookmakers as $bookmaker) {
                if (empty($bookmaker->bets)) continue;

                foreach ($bookmaker->bets as $bet) {
                    if ($bet->id == 12 && !empty($bet->values)) { // Double Chance market
                        foreach ($bet->values as $value) {
                            if ($doubleChanceType === '1x' && $value->value === 'Home/Draw') {
                                $odd = (float) $value->odd;
                                $maxOdd = max($maxOdd, $odd);
                            } elseif ($doubleChanceType === 'x2' && $value->value === 'Draw/Away') {
                                $odd = (float) $value->odd;
                                $maxOdd = max($maxOdd, $odd);
                            }
                        }
                    }
                }
            }
        }

        return $maxOdd;
    }

    /**
     * Get outcome description with recommendation
     */
    private function getOutcomeWithRecommendation(Fixture $fixture, $statisticData, array $recommendedBet): string
    {
        $prettyName = $statisticData->pretty_name ?? '';
        $configPrettyName = $statisticData->config_pretty_name ?? '';
        $value = $statisticData->value ?? 0;
        $count = $statisticData->count ?? 0;

        // Create streak description
        $streakDescription = "{$prettyName} - {$configPrettyName} ({$value}/{$count})";

        // Add recommendation
        $recommendation = $recommendedBet['description'] ?? '';

        if ($recommendation) {
            return "{$streakDescription} → {$recommendation}";
        }

        return $streakDescription;
    }

    /**
     * Get outcome description from streak data (OLD METHOD - keeping for reference)
     */
    private function getOutcomeFromStreak($statisticData): string
    {
        $prettyName = $statisticData->pretty_name ?? '';
        $configPrettyName = $statisticData->config_pretty_name ?? '';
        $value = $statisticData->value ?? 0;
        $count = $statisticData->count ?? 0;

        // Create a descriptive outcome based on the streak data
        if ($configPrettyName === 'Vitórias') {
            return "{$prettyName} vai ganhar";
        }

        return "{$prettyName} - {$configPrettyName} ({$value}/{$count})";
    }

    /**
     * Get API odd from raw odds data for specific market_id and bookmaker_id
     */
    private function getApiOddFromRawData(array $rawOdds, int $marketId, int $bookmakerId): float
    {
        foreach ($rawOdds as $entry) {
            if (empty($entry->bookmakers)) {
                continue;
            }

            foreach ($entry->bookmakers as $bookmaker) {
                if ($bookmaker->id != $bookmakerId || empty($bookmaker->bets)) {
                    continue;
                }

                foreach ($bookmaker->bets as $bet) {
                    if ($bet->id != $marketId || empty($bet->values)) {
                        continue;
                    }

                    // Get the highest odd from this market
                    $maxOdd = 0.0;
                    foreach ($bet->values as $value) {
                        $oddValue = (float) $value->odd;
                        if ($oddValue > $maxOdd) {
                            $maxOdd = $oddValue;
                        }
                    }

                    return $maxOdd;
                }
            }
        }

        return 0.0;
    }

    /**
     * Get the correct API odd for the team based on pretty_name
     */
    private function getCorrectApiOddForTeam(array $rawOdds, Fixture $fixture, string $prettyName, int $bookmakerId): float
    {
        // Load team relationships
        $fixture->load(['homeTeam', 'awayTeam']);

        // Determine which team the streak belongs to
        $isHomeTeam = $fixture->homeTeam && str_contains($prettyName, $fixture->homeTeam->name);
        $isAwayTeam = $fixture->awayTeam && str_contains($prettyName, $fixture->awayTeam->name);

        if (!$isHomeTeam && !$isAwayTeam) {
            return 0.0; // Can't determine team
        }

        // Find the correct odd value based on team
        foreach ($rawOdds as $entry) {
            if (empty($entry->bookmakers)) {
                continue;
            }

            foreach ($entry->bookmakers as $bookmaker) {
                if ($bookmaker->id != $bookmakerId || empty($bookmaker->bets)) {
                    continue;
                }

                foreach ($bookmaker->bets as $bet) {
                    if ($bet->id != 1 || empty($bet->values)) { // market_id = 1 for 1x2
                        continue;
                    }

                    foreach ($bet->values as $value) {
                        // Match the correct outcome based on team
                        if ($isHomeTeam && $value->value === 'Home') {
                            return (float) $value->odd;
                        } elseif ($isAwayTeam && $value->value === 'Away') {
                            return (float) $value->odd;
                        }
                    }
                }
            }
        }

        return 0.0;
    }
}