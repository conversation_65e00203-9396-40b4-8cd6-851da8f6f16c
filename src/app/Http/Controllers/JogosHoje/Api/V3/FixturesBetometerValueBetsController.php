<?php

namespace App\Http\Controllers\JogosHoje\Api\V3;

use App\Http\Controllers\Controller;
use App\Models\JogosHoje\{Fixture, Statistic};
use App\Services\JogosHoje\{ValueBetService, BetometerStreaksService};
use Illuminate\Http\{JsonResponse, Request};

class FixturesBetometerValueBetsController extends Controller
{
    public function __construct(private readonly ValueBetService $service, private readonly BetometerStreaksService $streaksService)
    {
    }

    public function __invoke(Request $request, Fixture $fixture): JsonResponse
    {
        $valueBets = collect($this->service->getValueBetsForFixture($fixture->id));
        $rawOdds   = $this->service->fetchRawOddsForFixture($fixture);
        $this->streaksService->handle([
            'homeTeamId' => $fixture->team_home,
            'awayTeamId' => $fixture->team_away,
        ]);

        $summary = $valueBets->map(function ($odd) {
            $prob = (float) $odd->true_probability;
            $fairOdd = $prob > 0 ? round(1 / $prob, 2) : null;
            $ev = $prob > 0 ? round(($prob * (float) $odd->odd_value) - 1, 4) : 0;

            return [
                'market_id'             => $odd->market_id,
                'bookmaker_id'          => $odd->bookmaker_id,
                'bookmaker_odd'         => (float) $odd->odd_value,
                'estimated_probability' => round($prob * 100, 2) . '%',
                'fair_odd'              => $fairOdd,
                'expected_value'        => $ev,
                'verdict'               => $odd->is_hot_bet ? 'Hot bet' : ($odd->is_trending_bet ? 'Trending bet' : ''),
            ];
        })->all();

        return response()->json([
            'fixture' => [
                'id'        => $fixture->id,
                'home_team' => $fixture->homeTeam?->name,
                'away_team' => $fixture->awayTeam?->name,
                'date'      => $fixture->date,
            ],
            'final_summary' => $summary,
        ]);
    }
}