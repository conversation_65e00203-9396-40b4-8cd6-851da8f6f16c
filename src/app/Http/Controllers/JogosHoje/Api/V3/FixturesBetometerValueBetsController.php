<?php

namespace App\Http\Controllers\JogosHoje\Api\V3;

use App\Http\Controllers\Controller;
use App\Models\JogosHoje\{Fixture, Market};
use App\Services\JogosHoje\{ValueBetService, BetometerStreaksService};
use Illuminate\Http\{JsonResponse, Request};

class FixturesBetometerValueBetsController extends Controller
{
    public function __construct(private readonly ValueBetService $service, private readonly BetometerStreaksService $streaksService)
    {
    }

    public function __invoke(Request $request, Fixture $fixture): JsonResponse
    {
        $valueBets = $this->service->getValueBetsForFixture($fixture->id);
        $this->streaksService->handle([
            'homeTeamId' => $fixture->team_home,
            'awayTeamId' => $fixture->team_away,
        ]);

        // Buscar todas las statistic_details con config_pretty_name = "Vitórias" para este fixture
        $statisticDetails = \App\Models\JogosHoje\StatisticDetails::where('fixture_id', $fixture->id)
            ->where('config_pretty_name', 'Vitórias')
            ->get();

        $valueBetsData = $statisticDetails->map(function ($statisticDetail) use ($fixture) {
            // True Probability: value/count de statistic_details
            $trueProbability = $statisticDetail->count > 0 ? $statisticDetail->value / $statisticDetail->count : 0.0;

            // Buscar la estadística relacionada para obtener el type
            $statistic = \App\Models\JogosHoje\Statistic::where('fixture_id', $fixture->id)
                ->where(function($q) use ($statisticDetail) {
                    $q->where('statistic_details_1_id', $statisticDetail->id)
                      ->orWhere('statistic_details_2_id', $statisticDetail->id);
                })
                ->first();

            // Market: si config_pretty_name es "Vitórias" entonces es "1x2"
            $marketType = '1x2';

            // Type: campo type de la tabla statistics
            $statisticType = $statistic?->type ?? 'unknown';

            // Fair odd: 1 / true_probability
            $fairOdd = $trueProbability > 0 ? 1 / $trueProbability : 0.0;

            // API odd: escoger el odd mayor de "1x2" basado en pretty_name del statistic_detail
            $prettyName = $statisticDetail->pretty_name ?? '';
            $apiOdd = $prettyName ? $this->service->getMax1X2Odd($fixture, $prettyName) : 0.0;

            // EV Calculation: (true_probability * api_odd) - 1
            $evCalculation = ($trueProbability * $apiOdd) - 1;

            // Verdict: basado en type y EV > 0
            $verdict = '';
            if ($statisticType === 'single' && $evCalculation > 0) {
                $verdict = 'Trending bet';
            } elseif ($statisticType === 'combined' && $evCalculation > 0) {
                $verdict = 'Hot bet';
            }

            return [
                'true_probability' => round($trueProbability, 4),
                'market'           => $marketType,
                'type'             => $statisticType,
                'fair_odd'         => round($fairOdd, 2),
                'api_odd'          => $apiOdd,
                'ev_calculation'   => round($evCalculation, 4),
                'verdict'          => $verdict,
                'team_name'        => $statisticDetail->pretty_name, // Para debug
                'config_pretty_name' => $statisticDetail->config_pretty_name, // Para debug
            ];
        });

        $summary = [
            'total_value_bets'         => $valueBets->count(),
            'positive_ev_bets'         => $valueBets->where('ev_positive', true)->count(),
            'average_odd'              => round($valueBets->avg('odd_value'), 2),
            'average_true_probability' => round($valueBets->avg('true_probability'), 4),
            'average_expected_value'   => round($valueBets->avg('expected_value'), 4),
            'hot_bets'                 => $valueBets->where('is_hot_bet', true)->count(),
            'trending_bets'            => $valueBets->where('is_trending_bet', true)->count(),
        ];

        return response()->json([
            'fixture' => [
                'id'        => $fixture->id,
                'home_team' => $fixture->homeTeam?->name,
                'away_team' => $fixture->awayTeam?->name,
                'date'      => $fixture->date,
            ],
            'value_bets' => $valueBetsData->all(),
            'summary'    => $summary,
            'streaks'    => $this->streaksService->result,
        ]);
    }
}