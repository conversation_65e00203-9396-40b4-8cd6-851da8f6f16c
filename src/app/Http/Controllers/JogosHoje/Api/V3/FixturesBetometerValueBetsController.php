<?php

namespace App\Http\Controllers\JogosHoje\Api\V3;

use App\Http\Controllers\Controller;
use App\Models\JogosHoje\{Fixture, Market};
use App\Services\JogosHoje\{ValueBetService, BetometerStreaksService};
use Illuminate\Http\{JsonResponse, Request};

class FixturesBetometerValueBetsController extends Controller
{
    public function __construct(private readonly ValueBetService $service, private readonly BetometerStreaksService $streaksService)
    {
    }

    public function __invoke(Request $request, Fixture $fixture): JsonResponse
    {
        $models    = collect($this->service->getValueBetsForFixture($fixture->id));
        $service   = $this->service;
        $valueBets = $models->map(function (\App\Models\Odd $odd) use ($fixture, $service) {
            $trueProbability = $service->calculateTrueProbability($fixture, $odd->market_id);
            $fairOdd         = $trueProbability > 0 ? 1 / $trueProbability : 0.0;
            $apiOdd          = $odd->odd_value;
            $evCalculation   = ($trueProbability * $apiOdd) - 1;
            $statisticType   = $service->getStatisticType($fixture, $odd->market_id);
            $marketType      = Market::find($odd->market_id)?->type;

            $statisticData  = $service->getStatisticData($fixture, $odd->market_id);
            $trueProbability = $statisticData['probability'];
            $marketType      = $statisticData['config_pretty_name'] === 'Vitórias' ? '1x2' : Market::find($odd->market_id)?->type;
            $apiOdd          = $service->getMax1X2Odd($fixture, $statisticData['pretty_name']);
            $fairOdd         = $trueProbability > 0 ? 1 / $trueProbability : 0.0;
            $evCalculation   = ($trueProbability * $apiOdd) - 1;
            $statisticType   = $statisticData['type'];

            $verdict = '';
            if ($statisticType === 'single' && $evCalculation > 0) {
                $verdict = 'Trending bet';
            } elseif ($statisticType === 'combined' && $evCalculation > 0) {
                $verdict = 'Hot bet';
            }

            return [
                'true_probability' => $trueProbability,
                'market'           => $marketType,
                'type'             => $statisticType,
                'fair_odd'         => $fairOdd,
                'api_odd'          => $apiOdd,
                'ev_calculation'   => $evCalculation,
                'verdict'          => $verdict,
            ];
        });
        $rawOdds   = $this->service->fetchRawOddsForFixture($fixture);
        $this->streaksService->handle([
            'homeTeamId' => $fixture->team_home,
            'awayTeamId' => $fixture->team_away,
        ]);

        $summary = [
            'total_value_bets'       => $models->count(),
            'positive_ev_bets'       => $models->where('ev_positive', true)->count(),
            'average_odd'            => $models->avg('odd_value'),
            'average_true_probability' => $models->avg('true_probability'),
            'average_expected_value' => $models->avg('expected_value'),
            'hot_bets'               => $models->where('is_hot_bet', true)->count(),
            'trending_bets'          => $models->where('is_trending_bet', true)->count(),
        ];

        return response()->json([
            'fixture' => [
                'id'        => $fixture->id,
                'home_team' => $fixture->homeTeam?->name,
                'away_team' => $fixture->awayTeam?->name,
                'date'      => $fixture->date,
            ],
            'value_bets' => $valueBets->all(),
            'api_odds'   => $rawOdds,
            'summary'    => $summary,
            'streaks'    => $this->streaksService->result,
        ]);
    }
}