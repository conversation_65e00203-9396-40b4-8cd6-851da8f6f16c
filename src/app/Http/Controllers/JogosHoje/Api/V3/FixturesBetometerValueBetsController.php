<?php

namespace App\Http\Controllers\JogosHoje\Api\V3;

use App\Http\Controllers\Controller;
use App\Models\JogosHoje\{Fixture, FixturePrediction};
use App\Services\JogosHoje\{ValueBetService, BetometerStreaksService};
use Illuminate\Http\{JsonResponse, Request};

class FixturesBetometerValueBetsController extends Controller
{
    public function __construct(private readonly ValueBetService $service, private readonly BetometerStreaksService $streaksService)
    {
    }

    public function __invoke(Request $request, Fixture $fixture): JsonResponse
    {
        // Get all statistics for this fixture (not filtered by market_id)
        $statisticsWithOdds = \Illuminate\Support\Facades\DB::select("
            SELECT
                s.*,
                sd.value,
                sd.count,
                sd.config_pretty_name,
                sd.pretty_name,
                sd.team_id
            FROM
                statistics s
            JOIN
                statistic_details sd ON sd.id = s.statistic_details_1_id
            WHERE
                s.fixture_id = ?
        ", [$fixture->id]);

        // Convert to collection for easier manipulation
        $statisticsCollection = collect($statisticsWithOdds);

        // Get all raw API odds for this fixture
        $rawOdds = $this->service->fetchRawOddsForFixture($fixture);

        // Process all available markets
        $allValueBets = collect();

        // Group statistics by market_id for processing
        $statisticsByMarket = $statisticsCollection->groupBy('market_id');

        foreach ($statisticsByMarket as $marketId => $marketStatistics) {
            foreach ($marketStatistics as $statisticData) {
                // True Probability = value/count from statistic_detail table
                $value = $statisticData->value ?? 0;
                $count = $statisticData->count ?? 0;
                $trueProbability = $count > 0 ? $value / $count : 0.0;

                // Adjust probability if it's 100% (5/5 or 7/7) to 90%
                if ($trueProbability >= 1.0) {
                    $trueProbability = 0.9; // 90%
                }

                // Fair odd = 1/true_probability (with protection against division by zero)
                $fairOdd = $trueProbability > 0 ? 1 / $trueProbability : 0.0;

                // Get the maximum API odd for this market
                $apiOdd = $this->service->getMaxOddForMarket($fixture, $marketId);

                // If api_odd is 0, skip this bet
                if ($apiOdd == 0) {
                    continue;
                }

                // EV = (true_probability * api_odd) - 1
                $evCalculation = ($trueProbability * $apiOdd) - 1;

                // Check if there's a prediction that supports this outcome
                // Use the streak market_id to determine which prediction to check
                $hasPredictionSupport = $this->checkPredictionSupportByStreakMarket($fixture, $marketId, $statisticData);

                // Verdict logic: 'single' + EV>0 = trending bet, 'combined' + EV>0 = hot bet
                // If has prediction support, upgrade to "Super Value bet"
                $verdict = '';
                if ($evCalculation > 0) {
                    if ($hasPredictionSupport) {
                        $verdict = 'Super Value bet';
                    } else {
                        $verdict = $statisticData->type === 'single' ? 'Trending bet' : 'Hot bet';
                    }
                }

                // Get outcome description from streak
                $outcome = $this->getOutcomeFromStreak($statisticData);

                $valueBet = [
                    'true_probability' => round($trueProbability, 4),
                    'market'           => $this->service->getMarketName($marketId),
                    'type'             => $statisticData->type ?? '',
                    'fair_odd'         => round($fairOdd, 2),
                    'api_odd'          => $apiOdd,
                    'ev_calculation'   => round($evCalculation, 4),
                    'verdict'          => $verdict,
                    'outcome'          => $outcome,
                    'has_prediction_support' => $hasPredictionSupport,
                    'market_id'        => $marketId,
                    'value'            => $value,
                    'count'            => $count,
                    'statistic_type'   => $statisticData->type ?? '',
                    'pretty_name'      => $statisticData->pretty_name ?? '',
                    'config_pretty_name' => $statisticData->config_pretty_name ?? ''
                ];

                $allValueBets->push($valueBet);
            }
        }

        // Filter to show only positive EV bets
        $valueBetsData = $allValueBets->filter(function ($bet) {
            return $bet['ev_calculation'] > 0;
        });

        // Filter to show only the highest api_odd bet per market
        $filteredValueBets = $valueBetsData->groupBy('market_id')
            ->map(function ($marketBets) {
                return $marketBets->sortByDesc('api_odd')->first();
            })
            ->values();

        // Debug info
        $debugInfo = [
            'fixture_id' => $fixture->id,
            'statistics_with_odds_count' => $statisticsCollection->count(),
            'value_bets_generated' => $valueBetsData->count(),
            'filtered_value_bets' => $filteredValueBets->count(),
            'statistics_sample' => $statisticsCollection->take(3)->map(function($stat) {
                return [
                    'market_id' => $stat->market_id,
                    'type' => $stat->type,
                    'value' => $stat->value,
                    'count' => $stat->count,
                ];
            }),
        ];

        // Get streaks specific to this fixture using your query
        $fixtureStreaks = \Illuminate\Support\Facades\DB::select("
            SELECT s.*, sd.value, sd.count
            FROM statistics s
            JOIN statistic_details sd ON sd.id = s.statistic_details_1_id
            WHERE s.fixture_id = ?
        ", [$fixture->id]);

        $summary = [
            'total_value_bets_found'   => $valueBetsData->count(),
            'highest_api_odd_shown'    => $filteredValueBets->count(),
            'positive_ev_bets'         => $valueBetsData->where('ev_calculation', '>', 0)->count(),
            'trending_bets'            => $valueBetsData->where('verdict', 'Trending bet')->count(),
            'hot_bets'                 => $valueBetsData->where('verdict', 'Hot bet')->count(),
            'super_value_bets'         => $valueBetsData->where('verdict', 'Super Value bet')->count(),
            'bets_with_prediction_support' => $valueBetsData->where('has_prediction_support', true)->count(),
        ];

        // Get predictions for this fixture
        $predictions = FixturePrediction::where('fixture_id', $fixture->id)->first();

        return response()->json([
            'fixture' => [
                'id'        => $fixture->id,
                'home_team' => $fixture->homeTeam?->name,
                'away_team' => $fixture->awayTeam?->name,
                'date'      => $fixture->date,
            ],
            'value_bets' => $filteredValueBets->all(),
            'predictions' => $predictions,
            'raw_api_odds' => $rawOdds,
            'summary'    => $summary,
            'streaks'    => $fixtureStreaks,
            'debug'      => $debugInfo,
        ]);
    }

    /**
     * Check if there's a prediction that supports this outcome based on streak market_id
     */
    private function checkPredictionSupportByStreakMarket(Fixture $fixture, int $streakMarketId, $statisticData): bool
    {
        // Get prediction for this fixture
        $prediction = FixturePrediction::where('fixture_id', $fixture->id)->first();

        if (!$prediction) {
            return false;
        }

        // Map streak market_id to prediction type
        switch ($streakMarketId) {
            case 1: // Streak market_id 1 -> 1X2 predictions
                return $this->check1X2PredictionSupport($prediction, $fixture, '');

            case 2: // Streak market_id 2 -> Cards predictions
                return $this->checkCardsPredictionSupport($prediction);

            case 3: // Streak market_id 3 -> Corners predictions
                return $this->checkCornersPredictionSupport($prediction);

            case 4: // Streak market_id 4 -> Goals predictions
                return $this->checkGoalsPredictionSupport($prediction, '');

            default:
                return false;
        }
    }

    /**
     * Check if there's a prediction that supports this outcome (OLD METHOD - keeping for reference)
     */
    private function checkPredictionSupport(Fixture $fixture, int $marketId, $statisticData): bool
    {
        // Get prediction for this fixture
        $prediction = FixturePrediction::where('fixture_id', $fixture->id)->first();

        if (!$prediction) {
            return false;
        }

        $prettyName = $statisticData->pretty_name ?? '';
        $configPrettyName = $statisticData->config_pretty_name ?? '';

        // Load team relationships
        $fixture->load(['homeTeam', 'awayTeam']);

        // Map market API IDs to their corresponding predictions
        // Based on the mapping you provided:
        switch ($marketId) {
            case 1:  // 1X2 WIN -> PREDICTIONS 1X2
                return $this->check1X2PredictionSupport($prediction, $fixture, $prettyName);

            case 3:  // 1ST HALF RESULT -> PREDICTIONS 1X2
                return $this->check1X2PredictionSupport($prediction, $fixture, $prettyName);

            case 5:  // TOTAL GOALS O/U -> PREDICTIONS GOALS
                return $this->checkGoalsPredictionSupport($prediction, $configPrettyName);

            case 8:  // BTTS (YES/NO) -> PREDICTIONS GOALS
                return $this->checkGoalsPredictionSupport($prediction, $configPrettyName);

            case 14: // 1ST TO SCORE -> PREDICTIONS GOALS
                return $this->checkGoalsPredictionSupport($prediction, $configPrettyName);

            case 45: // TOTAL CORNERS O/U -> PREDICTIONS CORNERS
                return $this->checkCornersPredictionSupport($prediction);

            case 80: // CARD O/U -> PREDICTIONS CARDS
                return $this->checkCardsPredictionSupport($prediction);

            default:
                return false;
        }
    }

    private function check1X2PredictionSupport(FixturePrediction $prediction, Fixture $fixture, string $prettyName): bool
    {
        $prediction1x2 = $prediction->{'1x2'};

        if (!$prediction1x2 || !isset($prediction1x2['status'])) {
            return false;
        }

        // Simply check if status is "available"
        return $prediction1x2['status'] === 'available';
    }

    private function checkGoalsPredictionSupport(FixturePrediction $prediction, string $configPrettyName): bool
    {
        $goalsData = $prediction->goals;

        if (!$goalsData || !isset($goalsData['status'])) {
            return false;
        }

        // Simply check if status is "available"
        return $goalsData['status'] === 'available';
    }

    private function checkCornersPredictionSupport(FixturePrediction $prediction): bool
    {
        $cornersData = $prediction->corners;

        if (!$cornersData || !isset($cornersData['status'])) {
            return false;
        }

        // Simply check if status is "available"
        return $cornersData['status'] === 'available';
    }

    private function checkCardsPredictionSupport(FixturePrediction $prediction): bool
    {
        $cardsData = $prediction->cards;

        if (!$cardsData || !isset($cardsData['status'])) {
            return false;
        }

        // Simply check if status is "available"
        return $cardsData['status'] === 'available';
    }

    /**
     * Get outcome description from streak data
     */
    private function getOutcomeFromStreak($statisticData): string
    {
        $prettyName = $statisticData->pretty_name ?? '';
        $configPrettyName = $statisticData->config_pretty_name ?? '';
        $value = $statisticData->value ?? 0;
        $count = $statisticData->count ?? 0;

        // Create a descriptive outcome based on the streak data
        if ($configPrettyName === 'Vitórias') {
            return "{$prettyName} vai ganhar";
        }

        return "{$prettyName} - {$configPrettyName} ({$value}/{$count})";
    }

    /**
     * Get API odd from raw odds data for specific market_id and bookmaker_id
     */
    private function getApiOddFromRawData(array $rawOdds, int $marketId, int $bookmakerId): float
    {
        foreach ($rawOdds as $entry) {
            if (empty($entry->bookmakers)) {
                continue;
            }

            foreach ($entry->bookmakers as $bookmaker) {
                if ($bookmaker->id != $bookmakerId || empty($bookmaker->bets)) {
                    continue;
                }

                foreach ($bookmaker->bets as $bet) {
                    if ($bet->id != $marketId || empty($bet->values)) {
                        continue;
                    }

                    // Get the highest odd from this market
                    $maxOdd = 0.0;
                    foreach ($bet->values as $value) {
                        $oddValue = (float) $value->odd;
                        if ($oddValue > $maxOdd) {
                            $maxOdd = $oddValue;
                        }
                    }

                    return $maxOdd;
                }
            }
        }

        return 0.0;
    }

    /**
     * Get the correct API odd for the team based on pretty_name
     */
    private function getCorrectApiOddForTeam(array $rawOdds, Fixture $fixture, string $prettyName, int $bookmakerId): float
    {
        // Load team relationships
        $fixture->load(['homeTeam', 'awayTeam']);

        // Determine which team the streak belongs to
        $isHomeTeam = $fixture->homeTeam && str_contains($prettyName, $fixture->homeTeam->name);
        $isAwayTeam = $fixture->awayTeam && str_contains($prettyName, $fixture->awayTeam->name);

        if (!$isHomeTeam && !$isAwayTeam) {
            return 0.0; // Can't determine team
        }

        // Find the correct odd value based on team
        foreach ($rawOdds as $entry) {
            if (empty($entry->bookmakers)) {
                continue;
            }

            foreach ($entry->bookmakers as $bookmaker) {
                if ($bookmaker->id != $bookmakerId || empty($bookmaker->bets)) {
                    continue;
                }

                foreach ($bookmaker->bets as $bet) {
                    if ($bet->id != 1 || empty($bet->values)) { // market_id = 1 for 1x2
                        continue;
                    }

                    foreach ($bet->values as $value) {
                        // Match the correct outcome based on team
                        if ($isHomeTeam && $value->value === 'Home') {
                            return (float) $value->odd;
                        } elseif ($isAwayTeam && $value->value === 'Away') {
                            return (float) $value->odd;
                        }
                    }
                }
            }
        }

        return 0.0;
    }
}