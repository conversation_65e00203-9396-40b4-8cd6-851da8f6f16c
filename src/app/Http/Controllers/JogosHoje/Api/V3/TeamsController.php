<?php

namespace App\Http\Controllers\JogosHoje\Api\V3;

use App\Http\Controllers\BaseController;
use App\Http\Resources\JogosHoje\TeamResource;
use App\Models\JogosHoje\{Fixture, FixtureEvent, FixtureStatistic, Standing, Team, TeamPlayer};
use App\Traits\FixturesTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\{Cache, Date};

class TeamsController extends BaseController
{
    use FixturesTrait;

    public function __construct(
        private readonly Team $teamModel,
        private readonly FixtureEvent $fixtureEventModel,
        private readonly FixtureStatistic $fixtureStatisticModel,
        private readonly Fixture $fixtureModel,
        private readonly Standing $standingModel,
        private readonly TeamPlayer $teamPlayerModel,
    ) {
    }

    public function index(Request $request): AnonymousResourceCollection|TeamResource
    {
        return Cache::remember(
            'teams-' . join('-', [$request->team_slug, $request->country_slug]),
            Date::now()->addDay(),
            function () use ($request) {
                if ($request->team_slug && $request->country_slug) {
                    $team = $this->teamModel->getBySlug($request->country_slug, $request->team_slug);
                    $team?->increasePopularity();
                    return new TeamResource($team);
                }

                return TeamResource::collection([]);
            }
        );
    }

    public function show(Request $request, Team $team): TeamResource
    {
        return Cache::remember(
            'teams-' . $team->id,
            Date::now()->addMonth(),
            fn() => new TeamResource($team)
        );
    }
}
