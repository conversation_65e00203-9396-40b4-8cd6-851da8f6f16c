<?php

namespace App\Http\Controllers\JogosHoje\Api\V3;

use App\Http\Controllers\Controller;
use App\Services\JogosHoje\BetanoFeedService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class BetanoOddsController extends Controller
{
    public function __construct(
        private readonly BetanoFeedService $betanoService
    ) {}

    /**
     * Get all football odds from Betano
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $odds = $this->betanoService->getAllFootballOdds();

            return response()->json([
                'success' => true,
                'data' => $odds,
                'count' => count($odds),
                'source' => 'Betano',
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to fetch odds from Betano',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get odds for a specific event
     */
    public function show(Request $request, string $eventId): JsonResponse
    {
        try {
            $odds = $this->betanoService->getEventOdds($eventId);

            if (empty($odds)) {
                return response()->json([
                    'success' => false,
                    'error' => 'Event not found',
                    'event_id' => $eventId
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $odds,
                'event_id' => $eventId,
                'source' => 'Betano',
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to fetch event odds',
                'message' => $e->getMessage(),
                'event_id' => $eventId
            ], 500);
        }
    }

    /**
     * Get odds for a specific league
     */
    public function league(Request $request, string $leagueId): JsonResponse
    {
        try {
            $odds = $this->betanoService->getLeagueOdds($leagueId);

            return response()->json([
                'success' => true,
                'data' => $odds,
                'count' => count($odds),
                'league_id' => $leagueId,
                'source' => 'Betano',
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to fetch league odds',
                'message' => $e->getMessage(),
                'league_id' => $leagueId
            ], 500);
        }
    }

    /**
     * Get today's odds only
     */
    public function today(Request $request): JsonResponse
    {
        try {
            $odds = $this->betanoService->getTodayOdds();

            return response()->json([
                'success' => true,
                'data' => $odds,
                'count' => count($odds),
                'date' => now()->toDateString(),
                'source' => 'Betano',
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to fetch today\'s odds',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get odds formatted for value bet calculations
     */
    public function valueBets(Request $request): JsonResponse
    {
        try {
            $odds = $this->betanoService->getOddsForValueBets();

            return response()->json([
                'success' => true,
                'data' => $odds,
                'count' => count($odds),
                'format' => 'value_bet_compatible',
                'source' => 'Betano',
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to fetch odds for value bets',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
