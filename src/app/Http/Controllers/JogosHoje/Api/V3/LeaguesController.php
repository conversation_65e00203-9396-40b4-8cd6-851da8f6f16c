<?php

namespace App\Http\Controllers\JogosHoje\Api\V3;

use App\Http\Controllers\BaseController;
use App\Http\Resources\JogosHoje\LeagueResource;
use App\Models\JogosHoje\League;
use App\Services\JogosHoje\LeagueRoundsService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\{Cache, Date};

class LeaguesController extends BaseController
{
    public function __construct(
        private readonly League $leagueModel,
        private readonly LeagueRoundsService $leagueRoundsService,
    ) {
    }

    public function index(Request $request): AnonymousResourceCollection|LeagueResource
    {
        return Cache::remember(
            'leagues-' . join('-', [$request->league_slug, $request->country_slug]),
            Date::now()->addMinutes(30),
            function () use ($request) {
                if ($request->league_slug && $request->country_slug) {
                    $league = $this->leagueModel->findBySlug($request->country_slug, $request->league_slug)?->first();

                    $league->rounds = $league ? $this->leagueRoundsService->getRounds($league) : [];
                    $league?->increasePopularity();
                    return new LeagueResource($league);
                }

                return LeagueResource::collection(
                    $this->leagueModel
                        ->where('is_main', 1)
                        ->where('is_favorite', 1)
                        ->orderBy('main_order')
                        ->get()
                );
            }
        );
    }

    public function show(Request $request, League $league): LeagueResource
    {
        return Cache::remember(
            'leagues-' . $league->id,
            Date::now()->addHour(),
            function () use ($league) {
                $league->rounds = $this->leagueRoundsService->getRounds($league);

                return new LeagueResource($league);
            }
        );
    }
}
