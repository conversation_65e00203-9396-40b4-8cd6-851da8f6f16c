<?php

namespace App\Http\Controllers\JogosHoje\Api\V3;

use App\Http\Controllers\Controller;
use App\Services\JogosHoje\SearchService;
use Illuminate\Http\{JsonResponse, Request};
class SearchController extends Controller
{
    public function __construct(
        private readonly SearchService $service
    ) {
    }

    public function __invoke(Request $request): JsonResponse
    {
        $term = $request->get('q');
        $type = $request->get('type', 'all');

        if (strlen($term) < 2) {
            return response()->json(['results' => []]);
        }

        $result = $this->service->search($term, $type);
        return response()
            ->json($result);
    }
}
