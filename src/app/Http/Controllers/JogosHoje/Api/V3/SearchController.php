<?php

namespace App\Http\Controllers\JogosHoje\Api\V3;

use App\Http\Controllers\Controller;
use App\Services\JogosHoje\SearchService;
use Illuminate\Http\{JsonResponse, Request};
class SearchController extends Controller
{
    public function __construct(
        private readonly SearchService $service
    ) {
    }

    public function __invoke(Request $request): JsonResponse
    {
        $term = $request->get('q');

        if (empty($term)) {
            return response()
                ->json([]);
        }

        $result = $this->service->search($term, $request->get('type'));
        return response()
            ->json($result);
    }
}
