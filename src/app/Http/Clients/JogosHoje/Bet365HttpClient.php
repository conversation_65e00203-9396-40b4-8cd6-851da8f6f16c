<?php

namespace App\Http\Clients\Bookmakers;

use GuzzleHttp\Client;

class Bet365HttpClient
{
    private Client $client;

    public function __construct()
    {
        $this->client = new Client([
            'base_uri' => config('services.bet365.base_url'),
            'auth' => [config('services.bet365.username'), config('services.bet365.password')],
        ]);
    }

    public function getOdds(array $params): mixed
    {
        $response = $this->client->get('/soccer/', ['query' => $params]);
        $xml = $response->getBody()->getContents();
        return simplexml_load_string($xml);
    }
}