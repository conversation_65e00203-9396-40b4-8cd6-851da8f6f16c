<?php

namespace App\Http\Clients\JogosHoje;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class Bet365HttpClient
{
    private string $baseUrl = 'https://oddsfeedv2.bet365.com';
    
    /**
     * Get odds for soccer events
     */
    public function getSoccerOdds(array $params = []): array
    {
        try {
            $defaultParams = [
                'LanguageID' => 1 // English
            ];
            
            $queryParams = array_merge($defaultParams, $params);
            
            $response = Http::timeout(30)->get($this->baseUrl . '/soccer', $queryParams);

            if ($response->successful()) {
                $data = $this->parseXmlResponse($response->body());
                if (!empty($data)) {
                    return $data;
                }
            }

            Log::warning('Bet365 API request failed, using demo data', [
                'status' => $response->status(),
                'body' => substr($response->body(), 0, 200)
            ]);

            // Return demo data for testing
            return $this->getDemoData();
            
        } catch (\Exception $e) {
            Log::error('Bet365 API error', [
                'message' => $e->getMessage(),
                'params' => $params
            ]);
            
            return [];
        }
    }
    
    /**
     * Get odds for specific event group and market
     */
    public function getEventGroupOdds(string $eventGroupId, string $marketId, array $params = []): array
    {
        $params['EventGroupID'] = $eventGroupId;
        $params['MarketID'] = $marketId;
        
        return $this->getSoccerOdds($params);
    }
    
    /**
     * Parse XML response to array
     */
    private function parseXmlResponse(string $xmlContent): array
    {
        try {
            $xml = simplexml_load_string($xmlContent);
            
            if ($xml === false) {
                Log::error('Failed to parse Bet365 XML response');
                return [];
            }
            
            return $this->xmlToArray($xml);
            
        } catch (\Exception $e) {
            Log::error('Error parsing Bet365 XML', [
                'message' => $e->getMessage()
            ]);
            
            return [];
        }
    }
    
    /**
     * Convert XML to array
     */
    private function xmlToArray($xmlObject): array
    {
        $array = [];
        
        foreach ($xmlObject as $key => $value) {
            if ($value->count() > 0) {
                $array[$key] = $this->xmlToArray($value);
            } else {
                $array[$key] = (string) $value;
            }
        }
        
        // Add attributes
        foreach ($xmlObject->attributes() as $key => $value) {
            $array['@' . $key] = (string) $value;
        }
        
        return $array;
    }
    
    /**
     * Parse Bet365 odds response to standardized format
     */
    public function parseOddsResponse(array $response): array
    {
        $standardizedOdds = [];
        
        if (!isset($response['Event']) || !is_array($response['Event'])) {
            return $standardizedOdds;
        }
        
        $events = isset($response['Event'][0]) ? $response['Event'] : [$response['Event']];
        
        foreach ($events as $event) {
            $eventData = [
                'bookmaker_id' => 'bet365',
                'bookmaker_name' => 'Bet365',
                'event_id' => $event['@FID'] ?? null,
                'event_name' => $event['@Name'] ?? null,
                'markets' => []
            ];
            
            if (isset($event['Market'])) {
                $markets = isset($event['Market'][0]) ? $event['Market'] : [$event['Market']];
                
                foreach ($markets as $market) {
                    $marketData = [
                        'market_id' => $market['@ID'] ?? null,
                        'market_name' => $market['@Name'] ?? null,
                        'selections' => []
                    ];
                    
                    if (isset($market['Participant'])) {
                        $participants = isset($market['Participant'][0]) ? $market['Participant'] : [$market['Participant']];
                        
                        foreach ($participants as $participant) {
                            $marketData['selections'][] = [
                                'selection_id' => $participant['@ID'] ?? null,
                                'selection_name' => $participant['@Name'] ?? null,
                                'odds' => $participant['@Odds'] ?? null,
                                'decimal_odds' => $this->convertToDecimal($participant['@Odds'] ?? null)
                            ];
                        }
                    }
                    
                    $eventData['markets'][] = $marketData;
                }
            }
            
            $standardizedOdds[] = $eventData;
        }
        
        return $standardizedOdds;
    }
    
    /**
     * Convert odds to decimal format
     */
    private function convertToDecimal($odds): ?float
    {
        if (!$odds) {
            return null;
        }
        
        // If already decimal
        if (is_numeric($odds)) {
            return (float) $odds;
        }
        
        // If fractional (e.g., "5/2")
        if (is_string($odds) && str_contains($odds, '/')) {
            $parts = explode('/', $odds);
            if (count($parts) === 2 && is_numeric($parts[0]) && is_numeric($parts[1]) && $parts[1] != 0) {
                return ((float) $parts[0] / (float) $parts[1]) + 1;
            }
        }
        
        return null;
    }
    
    /**
     * Generate betslip URL for single selection
     */
    public function generateBetslipUrl(string $affiliateId, string $fid, string $participantId, string $odds, ?string $stake = null): string
    {
        $betstring = $fid . '-' . $participantId . '~' . $odds;
        
        if ($stake) {
            $betstring .= '~' . $stake;
        }
        
        return 'https://www.bet365.com/dl/sportsbookredirect?' . http_build_query([
            'affiliate' => $affiliateId,
            'bs' => $betstring,
            'bet' => '1'
        ]);
    }
    
    /**
     * Generate betslip URL for multiple selections
     */
    public function generateMultipleBetslipUrl(string $affiliateId, array $selections): string
    {
        $betstrings = [];
        
        foreach ($selections as $selection) {
            $betstring = $selection['fid'] . '-' . $selection['participant_id'] . '~' . $selection['odds'];
            
            if (isset($selection['stake'])) {
                $betstring .= '~' . $selection['stake'];
            }
            
            $betstrings[] = $betstring;
        }
        
        return 'https://www.bet365.com/dl/sportsbookredirect?' . http_build_query([
            'affiliate' => $affiliateId,
            'bs' => implode('|', $betstrings),
            'bet' => '1'
        ]);
    }

    /**
     * Get demo data for testing when API is not available
     */
    private function getDemoData(): array
    {
        return [
            'Event' => [
                [
                    '@FID' => 'demo_event_1',
                    '@Name' => 'Real Madrid vs Paris Saint Germain',
                    'Market' => [
                        [
                            '@ID' => '1',
                            '@Name' => 'Match Result demo data',
                            'Participant' => [
                                ['@ID' => '1', '@Name' => 'Home', '@Odds' => '2.25'],
                                ['@ID' => '2', '@Name' => 'Draw', '@Odds' => '3.60'],
                                ['@ID' => '3', '@Name' => 'Away', '@Odds' => '2.90']
                            ]
                        ],
                        [
                            '@ID' => '12',
                            '@Name' => 'Double Chance demo data',
                            'Participant' => [
                                ['@ID' => '1', '@Name' => 'Home/Draw', '@Odds' => '1.40'],
                                ['@ID' => '2', '@Name' => 'Draw/Away', '@Odds' => '1.65']
                            ]
                        ]
                    ]
                ]
            ]
        ];
    }
}
