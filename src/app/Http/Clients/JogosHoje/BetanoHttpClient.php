<?php

namespace App\Http\Clients\Bookmakers;

use GuzzleHttp\Client;

class BetanoHttpClient
{
    private Client $client;

    public function __construct()
    {
        $this->client = new Client();
    }

    public function getCombinedOdds(string $sport = 'FOOT', string $lang = 'pt'): mixed
    {
        $endpoint = rtrim(config('services.betano.base_url'), '/').'/adserve';
        $query = [
            'type'  => 'OddsComparisonFeedAndLiveEvents',
            'lang'  => $lang,
            'sport' => $sport,
        ];

        $response = $this->client->get($endpoint, ['query' => $query]);
        return json_decode($response->getBody()->getContents(), true);
    }

    public function getEventOdds(int $eventId, string $sport = 'FOOT', string $lang = 'pt'): mixed
    {
        $endpoint = rtrim(config('services.betano.base_url'), '/').'/adserve';
        $query = [
            'type'    => 'OddsComparisonFeed',
            'lang'    => $lang,
            'sport'   => $sport,
            'eventid' => $eventId,
        ];

        $response = $this->client->get($endpoint, ['query' => $query]);
        return json_decode($response->getBody()->getContents(), true);
    }
}