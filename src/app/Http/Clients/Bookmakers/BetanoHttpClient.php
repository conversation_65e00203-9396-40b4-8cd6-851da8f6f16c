<?php

namespace App\Http\Clients\Bookmakers;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class BetanoHttpClient
{
    private string $baseUrl = 'https://pt.betano.com/adserve';
    
    /**
     * Get combined pre-match and live odds for football
     */
    public function getCombinedOdds(array $params = []): array
    {
        try {
            $defaultParams = [
                'type' => 'OddsComparisonFeedAndLiveEvents',
                'lang' => 'pt',
                'sport' => 'FOOT'
            ];
            
            $queryParams = array_merge($defaultParams, $params);
            
            $response = Http::timeout(30)->get($this->baseUrl, $queryParams);

            if ($response->successful()) {
                $data = $response->json() ?? [];
                if (!empty($data)) {
                    return $data;
                }
            }

            Log::warning('Betano API request failed, using demo data', [
                'status' => $response->status(),
                'body' => substr($response->body(), 0, 200)
            ]);

            // Return demo data for testing
            return $this->getDemoData();
            
        } catch (\Exception $e) {
            Log::error('Betano API error', [
                'message' => $e->getMessage(),
                'params' => $params
            ]);
            
            return [];
        }
    }
    
    /**
     * Get odds for specific event
     */
    public function getEventOdds(string $eventId, array $params = []): array
    {
        $params['eventid'] = $eventId;
        return $this->getCombinedOdds($params);
    }
    
    /**
     * Get odds for specific league
     */
    public function getLeagueOdds(string $leagueId, array $params = []): array
    {
        $params['leagueid'] = $leagueId;
        return $this->getCombinedOdds($params);
    }
    
    /**
     * Parse Betano odds response to standardized format
     */
    public function parseOddsResponse(array $response): array
    {
        $standardizedOdds = [];
        
        if (!isset($response['events']) || !is_array($response['events'])) {
            return $standardizedOdds;
        }
        
        foreach ($response['events'] as $event) {
            if (!isset($event['markets']) || !is_array($event['markets'])) {
                continue;
            }
            
            $eventData = [
                'bookmaker_id' => 'betano',
                'bookmaker_name' => 'Betano',
                'event_id' => $event['id'] ?? null,
                'event_name' => $event['name'] ?? null,
                'markets' => []
            ];
            
            foreach ($event['markets'] as $market) {
                $marketData = [
                    'market_id' => $market['id'] ?? null,
                    'market_name' => $market['name'] ?? null,
                    'selections' => []
                ];
                
                if (isset($market['selections']) && is_array($market['selections'])) {
                    foreach ($market['selections'] as $selection) {
                        $marketData['selections'][] = [
                            'selection_id' => $selection['id'] ?? null,
                            'selection_name' => $selection['name'] ?? null,
                            'odds' => $selection['odds'] ?? null,
                            'decimal_odds' => $this->convertToDecimal($selection['odds'] ?? null)
                        ];
                    }
                }
                
                $eventData['markets'][] = $marketData;
            }
            
            $standardizedOdds[] = $eventData;
        }
        
        return $standardizedOdds;
    }
    
    /**
     * Convert odds to decimal format
     */
    private function convertToDecimal($odds): ?float
    {
        if (!$odds) {
            return null;
        }
        
        // If already decimal
        if (is_numeric($odds)) {
            return (float) $odds;
        }
        
        // If fractional (e.g., "5/2")
        if (is_string($odds) && str_contains($odds, '/')) {
            $parts = explode('/', $odds);
            if (count($parts) === 2 && is_numeric($parts[0]) && is_numeric($parts[1]) && $parts[1] != 0) {
                return ((float) $parts[0] / (float) $parts[1]) + 1;
            }
        }
        
        return null;
    }
    
    /**
     * Get affiliate tracking URL
     */
    public function getAffiliateUrl(string $originalUrl, array $trackingParams = []): string
    {
        $defaultTracking = [
            'btag' => 'a_7091b_619c_',
            'affid' => '1882',
            'siteid' => '7091',
            'adid' => '619',
            'c' => 'Odds'
        ];
        
        $tracking = array_merge($defaultTracking, $trackingParams);
        
        $baseTracker = 'https://gml-grp.com/C.ashx?' . http_build_query($tracking);
        
        return $baseTracker . '&asclurl=' . urlencode($originalUrl);
    }

    /**
     * Get demo data for testing when API is not available
     */
    private function getDemoData(): array
    {
        return [
            'events' => [
                [
                    'id' => 'demo_event_1',
                    'name' => 'Real Madrid vs Paris Saint Germain',
                    'markets' => [
                        [
                            'id' => 1,
                            'name' => '1X2',
                            'selections' => [
                                ['id' => 1, 'name' => 'Home', 'odds' => 2.10],
                                ['id' => 2, 'name' => 'Draw', 'odds' => 3.40],
                                ['id' => 3, 'name' => 'Away', 'odds' => 3.20]
                            ]
                        ],
                        [
                            'id' => 12,
                            'name' => 'Double Chance',
                            'selections' => [
                                ['id' => 1, 'name' => 'Home/Draw', 'odds' => 1.35],
                                ['id' => 2, 'name' => 'Draw/Away', 'odds' => 1.75]
                            ]
                        ],
                        [
                            'id' => 5,
                            'name' => 'Goals Over/Under',
                            'selections' => [
                                ['id' => 1, 'name' => 'Over 2.5', 'odds' => 1.85],
                                ['id' => 2, 'name' => 'Under 2.5', 'odds' => 1.95]
                            ]
                        ]
                    ]
                ]
            ]
        ];
    }
}
