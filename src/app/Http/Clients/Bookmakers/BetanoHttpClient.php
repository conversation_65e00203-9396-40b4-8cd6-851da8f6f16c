<?php

namespace App\Http\Clients\Bookmakers;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class BetanoHttpClient
{
    private string $country = 'PT'; // Default country

    /**
     * Set country for dynamic domain selection
     */
    public function setCountry(string $country): self
    {
        $this->country = strtoupper($country);
        return $this;
    }

    /**
     * Get base URL for specific country
     */
    private function getBaseUrlForCountry(string $country): string
    {
        return match (strtoupper($country)) {
            'BR' => 'https://br.betano.com',
            'PT' => 'https://pt.betano.com',
            'ES' => 'https://es.betano.com',
            'IT' => 'https://it.betano.com',
            'DE' => 'https://de.betano.com',
            'RO' => 'https://ro.betano.com',
            default => 'https://pt.betano.com', // fallback
        };
    }

    /**
     * Get current base URL with /adserve endpoint
     */
    private function getBaseUrl(): string
    {
        return $this->getBaseUrlForCountry($this->country) . '/adserve';
    }

    /**
     * Get referer URL for current country
     */
    private function getRefererUrl(): string
    {
        return $this->getBaseUrlForCountry($this->country) . '/';
    }

    /**
     * Get combined pre-match and live odds for football
     */
    public function getCombinedOdds(array $params = []): array
    {
        try {
            // Check rate limit
            $rateLimitKey = 'betano:rate-limit:global';
            if (Cache::has($rateLimitKey)) {
                Log::info('[BETANO] Request ignored due to active rate limit.');
                return $this->getDemoData();
            }

            $defaultParams = [
                'type' => 'OddsComparisonFeedAndLiveEvents',
                'lang' => strtolower($this->country),
                'sport' => 'FOOT'
            ];

            $queryParams = array_merge($defaultParams, $params);
            $baseUrl = $this->getBaseUrl();
            $refererUrl = $this->getRefererUrl();

            Log::info('[BETANO] Fetching odds', [
                'country' => $this->country,
                'url' => $baseUrl,
                'params' => $queryParams
            ]);

            $response = Http::withHeaders([
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0 Safari/537.36',
                'Accept' => 'application/json, text/plain, */*',
                'Accept-Language' => 'pt-PT,pt;q=0.9,en;q=0.8',
                'Referer' => $refererUrl,
                'Cache-Control' => 'no-cache',
                'Pragma' => 'no-cache',
            ])->timeout(30)->get($baseUrl, $queryParams);

            if ($response->successful()) {
                $data = $response->json() ?? [];
                if (!empty($data)) {
                    // Set rate limit after successful request
                    Cache::put($rateLimitKey, true, now()->addMinutes(5));

                    Log::info('[BETANO] Successfully fetched odds', [
                        'country' => $this->country,
                        'events_count' => count($data['events'] ?? [])
                    ]);

                    return $data;
                }
            }

            // Handle different error types
            $this->handleApiError($response);

            // Return demo data for testing
            return $this->getDemoData();
            
        } catch (\Exception $e) {
            Log::error('Betano API error', [
                'message' => $e->getMessage(),
                'params' => $params
            ]);
            
            return [];
        }
    }
    
    /**
     * Get odds for specific event
     */
    public function getEventOdds(string $eventId, array $params = []): array
    {
        $params['eventid'] = $eventId;
        return $this->getCombinedOdds($params);
    }
    
    /**
     * Get odds for specific league
     */
    public function getLeagueOdds(string $leagueId, array $params = []): array
    {
        $params['leagueid'] = $leagueId;
        return $this->getCombinedOdds($params);
    }
    
    /**
     * Parse Betano odds response to standardized format
     */
    public function parseOddsResponse(array $response): array
    {
        $standardizedOdds = [];
        
        if (!isset($response['events']) || !is_array($response['events'])) {
            return $standardizedOdds;
        }
        
        foreach ($response['events'] as $event) {
            if (!isset($event['markets']) || !is_array($event['markets'])) {
                continue;
            }
            
            $eventData = [
                'bookmaker_id' => 'betano',
                'bookmaker_name' => 'Betano',
                'event_id' => $event['id'] ?? null,
                'event_name' => $event['name'] ?? null,
                'markets' => []
            ];
            
            foreach ($event['markets'] as $market) {
                $marketData = [
                    'market_id' => $market['id'] ?? null,
                    'market_name' => $market['name'] ?? null,
                    'selections' => []
                ];
                
                if (isset($market['selections']) && is_array($market['selections'])) {
                    foreach ($market['selections'] as $selection) {
                        $marketData['selections'][] = [
                            'selection_id' => $selection['id'] ?? null,
                            'selection_name' => $selection['name'] ?? null,
                            'odds' => $selection['odds'] ?? null,
                            'decimal_odds' => $this->convertToDecimal($selection['odds'] ?? null)
                        ];
                    }
                }
                
                $eventData['markets'][] = $marketData;
            }
            
            $standardizedOdds[] = $eventData;
        }
        
        return $standardizedOdds;
    }
    
    /**
     * Convert odds to decimal format
     */
    private function convertToDecimal($odds): ?float
    {
        if (!$odds) {
            return null;
        }
        
        // If already decimal
        if (is_numeric($odds)) {
            return (float) $odds;
        }
        
        // If fractional (e.g., "5/2")
        if (is_string($odds) && str_contains($odds, '/')) {
            $parts = explode('/', $odds);
            if (count($parts) === 2 && is_numeric($parts[0]) && is_numeric($parts[1]) && $parts[1] != 0) {
                return ((float) $parts[0] / (float) $parts[1]) + 1;
            }
        }
        
        return null;
    }
    
    /**
     * Get affiliate tracking URL
     */
    public function getAffiliateUrl(string $originalUrl, array $trackingParams = []): string
    {
        $defaultTracking = [
            'btag' => 'a_7091b_619c_',
            'affid' => '1882',
            'siteid' => '7091',
            'adid' => '619',
            'c' => 'Odds'
        ];
        
        $tracking = array_merge($defaultTracking, $trackingParams);
        
        $baseTracker = 'https://gml-grp.com/C.ashx?' . http_build_query($tracking);
        
        return $baseTracker . '&asclurl=' . urlencode($originalUrl);
    }

    /**
     * Handle API errors with detailed logging
     */
    private function handleApiError($response): void
    {
        $status = $response->status();
        $body = substr($response->body(), 0, 500);

        Log::warning('[BETANO] API request failed', [
            'country' => $this->country,
            'status' => $status,
            'body_preview' => $body
        ]);

        switch ($status) {
            case 403:
                Log::error('[BETANO] Possible Cloudflare block - check User-Agent and IP', [
                    'country' => $this->country,
                    'suggestion' => 'Consider rotating User-Agent or using proxy'
                ]);
                break;

            case 429:
                Log::error('[BETANO] Rate limit exceeded', [
                    'country' => $this->country,
                    'suggestion' => 'Increase delay between requests'
                ]);
                break;

            case 503:
                Log::error('[BETANO] Service unavailable', [
                    'country' => $this->country,
                    'suggestion' => 'Betano API may be down temporarily'
                ]);
                break;

            case 404:
                Log::warning('[BETANO] Endpoint not found', [
                    'country' => $this->country,
                    'suggestion' => 'Check if API endpoint is correct for this country'
                ]);
                break;

            default:
                if ($status >= 500) {
                    Log::error('[BETANO] Server error', [
                        'country' => $this->country,
                        'status' => $status
                    ]);
                } else {
                    Log::warning('[BETANO] Client error', [
                        'country' => $this->country,
                        'status' => $status
                    ]);
                }
        }

        // Check for Cloudflare-specific content
        if (str_contains($body, 'Cloudflare') || str_contains($body, 'cf-ray')) {
            Log::error('[BETANO] Cloudflare protection detected', [
                'country' => $this->country,
                'suggestion' => 'Need to implement Cloudflare bypass or use different approach'
            ]);
        }
    }

    /**
     * Get demo data for testing when API is not available
     */
    private function getDemoData(): array
    {
        return [
            'events' => [
                [
                    'id' => 'demo_event_1',
                    'name' => 'Real Madrid vs Paris Saint Germain',
                    'markets' => [
                        [
                            'id' => 1,
                            'name' => '1X2',
                            'selections' => [
                                ['id' => 1, 'name' => 'Home', 'odds' => 2.10],
                                ['id' => 2, 'name' => 'Draw', 'odds' => 3.40],
                                ['id' => 3, 'name' => 'Away', 'odds' => 3.20]
                            ]
                        ],
                        [
                            'id' => 12,
                            'name' => 'Double Chance',
                            'selections' => [
                                ['id' => 1, 'name' => 'Home/Draw', 'odds' => 1.35],
                                ['id' => 2, 'name' => 'Draw/Away', 'odds' => 1.75]
                            ]
                        ],
                        [
                            'id' => 5,
                            'name' => 'Goals Over/Under',
                            'selections' => [
                                ['id' => 1, 'name' => 'Over 2.5', 'odds' => 1.85],
                                ['id' => 2, 'name' => 'Under 2.5', 'odds' => 1.95]
                            ]
                        ]
                    ]
                ]
            ]
        ];
    }
}
