<?php

namespace App\Http\Clients\Bookmakers;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class BetanoHttpClient
{
    private string $country = 'PT'; // Default country

    /**
     * Set country for dynamic domain selection
     */
    public function setCountry(string $country): self
    {
        $this->country = strtoupper($country);
        return $this;
    }

    /**
     * Get base URL for specific country
     */
    private function getBaseUrlForCountry(string $country): string
    {
        return match (strtoupper($country)) {
            'BR' => 'https://br.betano.com',
            'PT' => 'https://pt.betano.com',
            'ES' => 'https://es.betano.com',
            'IT' => 'https://it.betano.com',
            'DE' => 'https://de.betano.com',
            'RO' => 'https://ro.betano.com',
            default => 'https://pt.betano.com', // fallback
        };
    }

    /**
     * Get current base URL with /adserve endpoint
     */
    private function getBaseUrl(): string
    {
        return $this->getBaseUrlForCountry($this->country) . '/adserve';
    }

    /**
     * Get referer URL for current country
     */
    private function getRefererUrl(): string
    {
        return $this->getBaseUrlForCountry($this->country) . '/';
    }

    /**
     * Get combined pre-match and live odds for football
     */
    public function getCombinedOdds(array $params = []): array
    {
        // Try multiple approaches to get real data
        $approaches = [
            'public_api',
            'mobile_api',
            'alternative_endpoint'
        ];

        foreach ($approaches as $approach) {
            try {
                $result = $this->tryApproach($approach, $params);
                if (!empty($result)) {
                    Log::info("[BETANO] Success with approach: {$approach}");
                    return $result;
                }
            } catch (\Throwable $e) {
                Log::warning("[BETANO] Approach {$approach} failed: " . $e->getMessage());
                continue;
            }
        }

        Log::warning('[BETANO] All approaches failed, using demo data');
        return $this->getDemoData();
    }

    /**
     * Try different approaches to get Betano data
     */
    private function tryApproach(string $approach, array $params): array
    {
        // Check rate limit
        $rateLimitKey = "betano:rate-limit:{$approach}";
        if (Cache::has($rateLimitKey)) {
            Log::info("[BETANO] Approach {$approach} rate limited");
            return [];
        }

        switch ($approach) {
            case 'public_api':
                return $this->tryPublicApi($params);
            case 'mobile_api':
                return $this->tryMobileApi($params);
            case 'alternative_endpoint':
                return $this->tryAlternativeEndpoint($params);
            default:
                return [];
        }
    }

    /**
     * Try public API approach
     */
    private function tryPublicApi(array $params): array
    {
        $baseUrl = $this->getBaseUrlForCountry($this->country);
        $apiUrl = $baseUrl . '/api/sport/football/events';

        Log::info('[BETANO] Trying public API', ['url' => $apiUrl]);

        $response = Http::withHeaders([
            'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept' => 'application/json',
            'Accept-Language' => 'pt-PT,pt;q=0.9',
            'Referer' => $baseUrl . '/',
            'Origin' => $baseUrl,
        ])->timeout(30)->get($apiUrl, [
            'limit' => 50,
            'sport' => 'football'
        ]);

        if ($response->successful()) {
            $data = $response->json();
            if (!empty($data)) {
                Cache::put("betano:rate-limit:public_api", true, now()->addMinutes(2));
                return $this->convertPublicApiResponse($data);
            }
        }

        $this->handleApiError($response);
        return [];
    }

    /**
     * Try mobile API approach
     */
    private function tryMobileApi(array $params): array
    {
        $baseUrl = $this->getBaseUrlForCountry($this->country);
        $mobileUrl = str_replace('https://', 'https://mobile.', $baseUrl) . '/api/events';

        Log::info('[BETANO] Trying mobile API', ['url' => $mobileUrl]);

        $response = Http::withHeaders([
            'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
            'Accept' => 'application/json',
            'Accept-Language' => 'pt-PT,pt;q=0.9',
        ])->timeout(30)->get($mobileUrl, [
            'sport' => 'football',
            'limit' => 20
        ]);

        if ($response->successful()) {
            $data = $response->json();
            if (!empty($data)) {
                Cache::put("betano:rate-limit:mobile_api", true, now()->addMinutes(2));
                return $this->convertMobileApiResponse($data);
            }
        }

        return [];
    }

    /**
     * Try alternative endpoint approach
     */
    private function tryAlternativeEndpoint(array $params): array
    {
        $defaultParams = [
            'type' => 'OddsComparisonFeedAndLiveEvents',
            'lang' => strtolower($this->country),
            'sport' => 'FOOT'
        ];

        $queryParams = array_merge($defaultParams, $params);
        $baseUrl = $this->getBaseUrl();

        Log::info('[BETANO] Trying alternative endpoint', [
            'country' => $this->country,
            'url' => $baseUrl,
            'params' => $queryParams
        ]);

        $response = Http::withHeaders([
            'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language' => 'pt-PT,pt;q=0.9,en-US;q=0.8,en;q=0.7',
            'Accept-Encoding' => 'gzip, deflate, br',
            'DNT' => '1',
            'Connection' => 'keep-alive',
            'Upgrade-Insecure-Requests' => '1',
            'Sec-Fetch-Dest' => 'document',
            'Sec-Fetch-Mode' => 'navigate',
            'Sec-Fetch-Site' => 'none',
            'Sec-Fetch-User' => '?1',
            'sec-ch-ua' => '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'sec-ch-ua-mobile' => '?0',
            'sec-ch-ua-platform' => '"macOS"',
        ])->timeout(45)->retry(3, 2000)->get($baseUrl, $queryParams);

        if ($response->successful()) {
            $data = $response->json() ?? [];
            if (!empty($data)) {
                // Set rate limit after successful request
                Cache::put("betano:rate-limit:alternative_endpoint", true, now()->addMinutes(5));

                Log::info('[BETANO] Successfully fetched odds via alternative endpoint', [
                    'country' => $this->country,
                    'events_count' => count($data['events'] ?? [])
                ]);

                return $data;
            }
        }

        $this->handleApiError($response);
        return [];
    }
    
    /**
     * Get odds for specific event
     */
    public function getEventOdds(string $eventId, array $params = []): array
    {
        $params['eventid'] = $eventId;
        return $this->getCombinedOdds($params);
    }
    
    /**
     * Get odds for specific league
     */
    public function getLeagueOdds(string $leagueId, array $params = []): array
    {
        $params['leagueid'] = $leagueId;
        return $this->getCombinedOdds($params);
    }
    
    /**
     * Parse Betano odds response to standardized format
     */
    public function parseOddsResponse(array $response): array
    {
        $standardizedOdds = [];
        
        if (!isset($response['events']) || !is_array($response['events'])) {
            return $standardizedOdds;
        }
        
        foreach ($response['events'] as $event) {
            if (!isset($event['markets']) || !is_array($event['markets'])) {
                continue;
            }
            
            $eventData = [
                'bookmaker_id' => 'betano',
                'bookmaker_name' => 'Betano',
                'event_id' => $event['id'] ?? null,
                'event_name' => $event['name'] ?? null,
                'markets' => []
            ];
            
            foreach ($event['markets'] as $market) {
                $marketData = [
                    'market_id' => $market['id'] ?? null,
                    'market_name' => $market['name'] ?? null,
                    'selections' => []
                ];
                
                if (isset($market['selections']) && is_array($market['selections'])) {
                    foreach ($market['selections'] as $selection) {
                        $marketData['selections'][] = [
                            'selection_id' => $selection['id'] ?? null,
                            'selection_name' => $selection['name'] ?? null,
                            'odds' => $selection['odds'] ?? null,
                            'decimal_odds' => $this->convertToDecimal($selection['odds'] ?? null)
                        ];
                    }
                }
                
                $eventData['markets'][] = $marketData;
            }
            
            $standardizedOdds[] = $eventData;
        }
        
        return $standardizedOdds;
    }
    
    /**
     * Convert odds to decimal format
     */
    private function convertToDecimal($odds): ?float
    {
        if (!$odds) {
            return null;
        }
        
        // If already decimal
        if (is_numeric($odds)) {
            return (float) $odds;
        }
        
        // If fractional (e.g., "5/2")
        if (is_string($odds) && str_contains($odds, '/')) {
            $parts = explode('/', $odds);
            if (count($parts) === 2 && is_numeric($parts[0]) && is_numeric($parts[1]) && $parts[1] != 0) {
                return ((float) $parts[0] / (float) $parts[1]) + 1;
            }
        }
        
        return null;
    }
    
    /**
     * Get affiliate tracking URL
     */
    public function getAffiliateUrl(string $originalUrl, array $trackingParams = []): string
    {
        $defaultTracking = [
            'btag' => 'a_7091b_619c_',
            'affid' => '1882',
            'siteid' => '7091',
            'adid' => '619',
            'c' => 'Odds'
        ];
        
        $tracking = array_merge($defaultTracking, $trackingParams);
        
        $baseTracker = 'https://gml-grp.com/C.ashx?' . http_build_query($tracking);
        
        return $baseTracker . '&asclurl=' . urlencode($originalUrl);
    }

    /**
     * Handle API errors with detailed logging
     */
    private function handleApiError($response): void
    {
        $status = $response->status();
        $body = substr($response->body(), 0, 500);

        Log::warning('[BETANO] API request failed', [
            'country' => $this->country,
            'status' => $status,
            'body_preview' => $body
        ]);

        switch ($status) {
            case 403:
                Log::error('[BETANO] Possible Cloudflare block - check User-Agent and IP', [
                    'country' => $this->country,
                    'suggestion' => 'Consider rotating User-Agent or using proxy'
                ]);
                break;

            case 429:
                Log::error('[BETANO] Rate limit exceeded', [
                    'country' => $this->country,
                    'suggestion' => 'Increase delay between requests'
                ]);
                break;

            case 503:
                Log::error('[BETANO] Service unavailable', [
                    'country' => $this->country,
                    'suggestion' => 'Betano API may be down temporarily'
                ]);
                break;

            case 404:
                Log::warning('[BETANO] Endpoint not found', [
                    'country' => $this->country,
                    'suggestion' => 'Check if API endpoint is correct for this country'
                ]);
                break;

            default:
                if ($status >= 500) {
                    Log::error('[BETANO] Server error', [
                        'country' => $this->country,
                        'status' => $status
                    ]);
                } else {
                    Log::warning('[BETANO] Client error', [
                        'country' => $this->country,
                        'status' => $status
                    ]);
                }
        }

        // Check for Cloudflare-specific content
        if (str_contains($body, 'Cloudflare') || str_contains($body, 'cf-ray')) {
            Log::error('[BETANO] Cloudflare protection detected', [
                'country' => $this->country,
                'suggestion' => 'Need to implement Cloudflare bypass or use different approach'
            ]);
        }
    }

    /**
     * Convert public API response to standard format
     */
    private function convertPublicApiResponse(array $data): array
    {
        if (empty($data['events'])) {
            return [];
        }

        $events = [];
        foreach ($data['events'] as $event) {
            $events[] = [
                'id' => $event['id'] ?? 'unknown',
                'name' => $event['name'] ?? 'Unknown Match',
                'markets' => $this->extractMarketsFromEvent($event)
            ];
        }

        return ['events' => $events];
    }

    /**
     * Convert mobile API response to standard format
     */
    private function convertMobileApiResponse(array $data): array
    {
        // Similar conversion logic for mobile API
        return $this->convertPublicApiResponse($data);
    }

    /**
     * Extract markets from event data
     */
    private function extractMarketsFromEvent(array $event): array
    {
        $markets = [];

        if (isset($event['markets'])) {
            foreach ($event['markets'] as $market) {
                $markets[] = [
                    'id' => $market['id'] ?? 1,
                    'name' => $market['name'] ?? '1X2',
                    'selections' => $this->extractSelectionsFromMarket($market)
                ];
            }
        }

        return $markets;
    }

    /**
     * Extract selections from market data
     */
    private function extractSelectionsFromMarket(array $market): array
    {
        $selections = [];

        if (isset($market['selections'])) {
            foreach ($market['selections'] as $selection) {
                $selections[] = [
                    'id' => $selection['id'] ?? 1,
                    'name' => $selection['name'] ?? 'Unknown',
                    'odds' => floatval($selection['odds'] ?? 1.0)
                ];
            }
        }

        return $selections;
    }

    /**
     * Get demo data for testing when API is not available
     */
    private function getDemoData(): array
    {
        return [
            'events' => [
                [
                    'id' => 'demo_event_1',
                    'name' => 'Real Madrid vs Paris Saint Germain',
                    'markets' => [
                        [
                            'id' => 1,
                            'name' => '1X2',
                            'selections' => [
                                ['id' => 1, 'name' => 'Home', 'odds' => 2.10],
                                ['id' => 2, 'name' => 'Draw', 'odds' => 3.40],
                                ['id' => 3, 'name' => 'Away', 'odds' => 3.20]
                            ]
                        ],
                        [
                            'id' => 12,
                            'name' => 'Double Chance',
                            'selections' => [
                                ['id' => 1, 'name' => 'Home/Draw', 'odds' => 1.35],
                                ['id' => 2, 'name' => 'Draw/Away', 'odds' => 1.75]
                            ]
                        ],
                        [
                            'id' => 5,
                            'name' => 'Goals Over/Under',
                            'selections' => [
                                ['id' => 1, 'name' => 'Over 2.5', 'odds' => 1.85],
                                ['id' => 2, 'name' => 'Under 2.5', 'odds' => 1.95]
                            ]
                        ]
                    ]
                ]
            ]
        ];
    }
}
