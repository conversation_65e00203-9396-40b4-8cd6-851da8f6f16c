<?php

namespace App\Http\Clients\Bookmakers;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class BetanoOddsClient
{
    private string $baseUrl;
    private string $language;
    private int $daysAhead;
    private int $maxEvents;

    public function __construct()
    {
        $this->baseUrl = config('services.betano.base_url');
        $this->language = config('services.betano.language');
        $this->daysAhead = config('services.betano.days_ahead');
        $this->maxEvents = config('services.betano.max_events');
    }

    /**
     * Get all football pre-match odds
     */
    public function getFootballOdds(array $filters = []): array
    {
        $cacheKey = 'betano_football_odds_' . md5(serialize($filters));
        
        return Cache::remember($cacheKey, now()->addMinutes(5), function () use ($filters) {
            $params = array_merge([
                'type' => 'OddsComparisonFeed',
                'lang' => $this->language,
                'sport' => 'FOOT',
                'daysAhead' => $this->daysAhead,
                'max' => $this->maxEvents,
            ], $filters);

            try {
                Log::info('[BETANO_CLIENT] Fetching football odds', ['params' => $params]);
                
                $response = Http::timeout(30)
                    ->withHeaders([
                        'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'Accept' => 'application/json, text/plain, */*',
                        'Accept-Language' => $this->language . ',en;q=0.9',
                        'Referer' => 'https://' . config('services.betano.domain') . '/',
                    ])
                    ->get($this->baseUrl, $params);

                if ($response->successful()) {
                    $data = $response->json();
                    Log::info('[BETANO_CLIENT] Successfully fetched odds', [
                        'events_count' => count($data['events'] ?? [])
                    ]);
                    return $data;
                } else {
                    Log::error('[BETANO_CLIENT] Failed to fetch odds', [
                        'status' => $response->status(),
                        'body' => $response->body()
                    ]);
                    return [];
                }
            } catch (\Exception $e) {
                Log::error('[BETANO_CLIENT] Exception fetching odds', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                return [];
            }
        });
    }

    /**
     * Get odds for a specific event
     */
    public function getEventOdds(string $eventId): array
    {
        $cacheKey = "betano_event_odds_{$eventId}";
        
        return Cache::remember($cacheKey, now()->addMinutes(5), function () use ($eventId) {
            $params = [
                'type' => 'OddsComparisonFeed',
                'lang' => $this->language,
                'sport' => 'FOOT',
                'eventid' => $eventId,
            ];

            try {
                Log::info('[BETANO_CLIENT] Fetching event odds', ['event_id' => $eventId]);
                
                $response = Http::timeout(30)
                    ->withHeaders([
                        'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'Accept' => 'application/json, text/plain, */*',
                        'Accept-Language' => $this->language . ',en;q=0.9',
                        'Referer' => 'https://' . config('services.betano.domain') . '/',
                    ])
                    ->get($this->baseUrl, $params);

                if ($response->successful()) {
                    $data = $response->json();
                    Log::info('[BETANO_CLIENT] Successfully fetched event odds', [
                        'event_id' => $eventId,
                        'markets_count' => count($data['markets'] ?? [])
                    ]);
                    return $data;
                } else {
                    Log::error('[BETANO_CLIENT] Failed to fetch event odds', [
                        'event_id' => $eventId,
                        'status' => $response->status(),
                        'body' => $response->body()
                    ]);
                    return [];
                }
            } catch (\Exception $e) {
                Log::error('[BETANO_CLIENT] Exception fetching event odds', [
                    'event_id' => $eventId,
                    'error' => $e->getMessage()
                ]);
                return [];
            }
        });
    }

    /**
     * Get odds for specific league
     */
    public function getLeagueOdds(string $leagueId): array
    {
        return $this->getFootballOdds(['leagueid' => $leagueId]);
    }

    /**
     * Get odds for today only
     */
    public function getTodayOdds(): array
    {
        return $this->getFootballOdds(['daysAhead' => 1]);
    }

    /**
     * Clear cache for all Betano odds
     */
    public function clearCache(): void
    {
        $tags = ['betano_football_odds', 'betano_event_odds'];
        foreach ($tags as $tag) {
            Cache::tags($tag)->flush();
        }
        Log::info('[BETANO_CLIENT] Cache cleared');
    }
}
