<?php

namespace App\Console\Commands;

use App\Http\Clients\Bookmakers\BetanoHttpClient;
use App\Services\JogosHoje\CountryOddsFilterService;
use Illuminate\Console\Command;

class TestCountryOdds extends Command
{
    protected $signature = 'test:country-odds {--country=PT : Country to test}';
    protected $description = 'Test odds fetching for different countries';

    public function __construct(
        private readonly BetanoHttpClient $betanoClient,
        private readonly CountryOddsFilterService $countryFilterService
    ) {
        parent::__construct();
    }

    public function handle(): int
    {
        $country = strtoupper($this->option('country'));
        
        $this->info("🌍 Testing odds for country: {$country}");
        $this->newLine();

        // Test country filter service
        $this->testCountryFilterService($country);
        $this->newLine();

        // Test Betano client with different countries
        $this->testBetanoClient($country);
        $this->newLine();

        return 0;
    }

    private function testCountryFilterService(string $country): void
    {
        $this->info("📋 Testing CountryOddsFilterService:");
        
        $allowedBookmakers = $this->countryFilterService->getAllowedBookmakers($country);
        $this->line("Allowed bookmakers for {$country}: " . implode(', ', $allowedBookmakers));
        
        $message = $this->countryFilterService->getRestrictedMessage($country);
        $this->line("Restriction message: {$message}");
        
        $supportedCountries = $this->countryFilterService->getSupportedCountries();
        $this->line("All supported countries: " . implode(', ', $supportedCountries));
    }

    private function testBetanoClient(string $country): void
    {
        $this->info("🎰 Testing BetanoHttpClient:");
        
        // Configure country
        $this->betanoClient->setCountry($country);
        
        $this->line("Testing with country: {$country}");
        
        try {
            $response = $this->betanoClient->getCombinedOdds(['max' => 1]);
            
            if (!empty($response)) {
                $this->info("✅ Success! Response received");
                
                if (isset($response['events'])) {
                    $this->line("Events found: " . count($response['events']));
                    
                    if (!empty($response['events'][0])) {
                        $event = $response['events'][0];
                        $this->line("Sample event: " . ($event['name'] ?? 'Unknown'));
                        $this->line("Markets: " . count($event['markets'] ?? []));
                    }
                } else {
                    $this->warn("No events in response");
                }
            } else {
                $this->warn("❌ Empty response");
            }
            
        } catch (\Throwable $e) {
            $this->error("❌ Error: " . $e->getMessage());
        }
        
        // Test different countries
        $this->newLine();
        $this->info("🔄 Testing multiple countries:");
        
        $testCountries = ['PT', 'BR', 'ES', 'IT'];
        
        foreach ($testCountries as $testCountry) {
            $this->betanoClient->setCountry($testCountry);
            
            try {
                $response = $this->betanoClient->getCombinedOdds(['max' => 1]);
                $status = !empty($response) ? '✅' : '❌';
                $this->line("{$status} {$testCountry}: " . (!empty($response) ? 'Success' : 'Failed'));
                
            } catch (\Throwable $e) {
                $this->line("❌ {$testCountry}: Error - " . $e->getMessage());
            }
        }
    }
}
