<?php

namespace App\Console\Commands;

use App\Helpers\Utils;
use App\Models\JogosHoje\Fixture;
use App\Models\Odd;
use App\Services\JogosHoje\ValueBetService;
use App\Services\JogosHoje\FixtureService;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Validator;

class CalculateValueBets extends Command
{
    protected $signature = 'calculate:value-bets {type=upcoming : live or upcoming fixtures}';
    protected $description = 'Calculate value bets for upcoming or live fixtures';

    public function __construct(
        private readonly ValueBetService $service,
        private readonly Fixture $fixtureModel,
        private readonly FixtureService $fixtureService,
    ) {
        parent::__construct();
    }

    public function handle(): int
    {
        $type = $this->validateTypeArgument();

        try {
            $this->fetchFixturesFromApi($type);

            $fixtures = $this->getFixtures($type);

            foreach ($fixtures as $fixture) {
                $this->processFixture($fixture);
            }

            return self::SUCCESS;
        } catch (\Throwable $e) {
            $this->error(Utils::parseExceptionErrorMessage($e));
            return self::FAILURE;
        }
    }

    private function validateTypeArgument(): string
    {
        $arguments = $this->arguments();

        Validator::validate($arguments, [
            'type' => 'required|string|in:live,upcoming',
        ]);

        return $arguments['type'];
    }

    private function fetchFixturesFromApi(string $type): void
    {
        $fetchType = $type === 'live' ? 'live' : 'next-days';

        $this->fixtureService->handle($fetchType);
    }

    private function getFixtures(string $type): Collection
    {
        return match ($type) {
            'live' => $this->fixtureModel->where('is_live', true)->get(),
            'upcoming' => $this->fixtureModel
                ->whereBetween('date', [now(), now()->addDays(5)])
                //->whereBetween('date', [now()->subDays(5), now()])
                ->whereNotNull('api_id')
                ->get(),
        };
    }

    private function processFixture(Fixture $fixture): void
    {
        $this->service->handleFixture($fixture);

        $models = collect($this->service->getValueBetsForFixture($fixture->id));
        $valueBets = $models->map(function (Odd $odd) {
            $fairOdd = $odd->true_probability > 0 ? 1 / $odd->true_probability : 0.0;

                return [
                    'outcome'              => $odd->option_name,
                    'estimated_probability'=> $odd->true_probability,
                    'fair_odd'             => $fairOdd,
                    'bookmaker_odd'        => $odd->odd_value,
                    'expected_value'       => $odd->expected_value,
                    'verdict'              => $odd->is_hot_bet ? 'Hot bet' : ($odd->is_trending_bet ? 'Trending bet' : ''),
                ];
            });
        $rawOdds   = $this->service->fetchRawOddsForFixture($fixture);

        $summary = $this->buildSummary($models);

        $this->line(json_encode([
            'fixture' => [
                'id'        => $fixture->id,
                'home_team' => $fixture->homeTeam?->name,
                'away_team' => $fixture->awayTeam?->name,
                'date'      => $fixture->date,
            ],
            'value_bets' => $valueBets->all(),
            'api_odds'   => $rawOdds,
            'summary'    => $summary,
        ], JSON_PRETTY_PRINT));
    }

    private function buildSummary(Collection $valueBets): array
    {

        $marketsBookmakers = $valueBets->unique(fn($item) => $item->market_id . '-' . $item->bookmaker_id)
            ->map(fn($item) => ['market_id' => $item->market_id, 'bookmaker_id' => $item->bookmaker_id])
            ->values()
            ->all();

        return [
            'markets_and_bookmakers' => $marketsBookmakers,
            'total_value_bets'       => $valueBets->count(),
            'positive_ev_bets'       => $valueBets->where('ev_positive', true)->count(),
            'average_odd'            => $valueBets->avg('odd_value'),
            'average_true_probability' => $valueBets->avg('true_probability'),
            'average_expected_value' => $valueBets->avg('expected_value'),
            'hot_bets'               => $valueBets->where('is_hot_bet', true)->count(),
            'trending_bets'          => $valueBets->where('is_trending_bet', true)->count(),
        ];
    }
}
