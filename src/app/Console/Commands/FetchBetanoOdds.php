<?php

namespace App\Console\Commands;

use App\Services\JogosHoje\BetanoOddsService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class FetchBetanoOdds extends Command
{
    protected $signature = 'betano:fetch-odds {--region=PT : Region to fetch odds for (PT, BR)} {--event-id= : Fetch odds for specific event ID}';
    protected $description = 'Fetch real odds from Betano API and save to database';

    public function __construct(
        private readonly BetanoOddsService $betanoOddsService
    ) {
        parent::__construct();
    }

    public function handle(): int
    {
        $region = strtoupper($this->option('region'));
        $eventId = $this->option('event-id');

        $this->info("🎰 Fetching real Betano odds...");
        $this->line("Region: {$region}");
        
        if ($eventId) {
            return $this->fetchSpecificEvent($eventId, $region);
        }

        return $this->fetchAllEvents($region);
    }

    /**
     * Fetch odds for all events
     */
    private function fetchAllEvents(string $region): int
    {
        $this->info("📡 Fetching all events from Betano {$region}...");
        
        try {
            $results = $this->betanoOddsService->fetchAndSaveAllOdds($region);
            
            if (empty($results)) {
                $this->warn("❌ No events found or processed");
                return self::FAILURE;
            }

            $this->info("✅ Successfully processed " . count($results) . " events");
            
            // Show summary
            $this->displaySummary($results);
            
            return self::SUCCESS;
            
        } catch (\Throwable $e) {
            $this->error("❌ Error fetching odds: " . $e->getMessage());
            Log::error('[FETCH_BETANO_ODDS] Command failed: ' . $e->getMessage());
            return self::FAILURE;
        }
    }

    /**
     * Fetch odds for specific event
     */
    private function fetchSpecificEvent(string $eventId, string $region): int
    {
        $this->info("📡 Fetching specific event: {$eventId} from Betano {$region}...");
        
        try {
            $result = $this->betanoOddsService->fetchOddsForEvent($eventId, $region);
            
            if (empty($result)) {
                $this->warn("❌ No data found for event {$eventId}");
                return self::FAILURE;
            }

            $this->info("✅ Successfully processed event {$eventId}");
            $this->displayEventDetails($result);
            
            return self::SUCCESS;
            
        } catch (\Throwable $e) {
            $this->error("❌ Error fetching event odds: " . $e->getMessage());
            Log::error('[FETCH_BETANO_ODDS] Event fetch failed: ' . $e->getMessage());
            return self::FAILURE;
        }
    }

    /**
     * Display summary of processed events
     */
    private function displaySummary(array $results): void
    {
        $this->newLine();
        $this->info("📊 Summary:");
        
        $totalEvents = count($results);
        $eventsWithFixtures = collect($results)->where('fixture_id', '!=', null)->count();
        $totalMarkets = collect($results)->sum('markets_processed');
        
        $this->table([
            'Metric', 'Value'
        ], [
            ['Total Events Processed', $totalEvents],
            ['Events Matched to Fixtures', $eventsWithFixtures],
            ['Events Without Fixtures', $totalEvents - $eventsWithFixtures],
            ['Total Markets Processed', $totalMarkets],
        ]);

        // Show sample events
        $this->newLine();
        $this->info("📋 Sample Events:");
        
        $sampleEvents = collect($results)->take(5);
        
        $this->table([
            'Betano Event ID', 'Event Name', 'Fixture ID', 'Markets'
        ], $sampleEvents->map(function($event) {
            return [
                $event['betano_event_id'],
                substr($event['event_name'], 0, 30) . (strlen($event['event_name']) > 30 ? '...' : ''),
                $event['fixture_id'] ?? 'Not matched',
                $event['markets_processed']
            ];
        })->toArray());

        if ($totalEvents > 5) {
            $this->line("... and " . ($totalEvents - 5) . " more events");
        }
    }

    /**
     * Display details of a specific event
     */
    private function displayEventDetails(array $result): void
    {
        $this->newLine();
        $this->info("📋 Event Details:");
        
        $this->table([
            'Field', 'Value'
        ], [
            ['Betano Event ID', $result['betano_event_id']],
            ['Event Name', $result['event_name']],
            ['Fixture ID', $result['fixture_id'] ?? 'Not matched'],
            ['Markets Processed', $result['markets_processed']],
        ]);

        if (!empty($result['markets'])) {
            $this->newLine();
            $this->info("🎯 Markets:");
            
            $this->table([
                'Market Name', 'Market ID', 'Selections'
            ], collect($result['markets'])->map(function($market) {
                return [
                    $market['market_name'],
                    $market['market_id'],
                    $market['selections_count']
                ];
            })->toArray());
        }
    }
}
