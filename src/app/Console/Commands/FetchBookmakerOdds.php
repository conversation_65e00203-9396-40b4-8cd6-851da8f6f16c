<?php

namespace App\Console\Commands;

use App\Http\Clients\Bookmakers\BetanoHttpClient;
use App\Http\Clients\Bookmakers\Bet365HttpClient;
use Illuminate\Console\Command;

class FetchBookmakerOdds extends Command
{
    protected $signature = 'odds:fetch-bookmakers {--fixture-id=}';
    protected $description = 'Fetch odds from Betano and Bet365 APIs';

    public function __construct(
        private readonly BetanoHttpClient $betanoClient,
        private readonly Bet365HttpClient $bet365Client
    ) {
        parent::__construct();
    }

    public function handle(): int
    {
        $this->info("Testing Bookmaker APIs...");

        // Test Betano API
        $this->info("Testing Betano API...");
        $betanoOdds = $this->betanoClient->getCombinedOdds(['max' => 2]);

        if (!empty($betanoOdds)) {
            $this->info("✅ Betano API: " . count($betanoOdds['events'] ?? []) . " events found");

            // Show sample data
            if (isset($betanoOdds['events'][0])) {
                $event = $betanoOdds['events'][0];
                $this->line("Sample event: " . ($event['name'] ?? 'Unknown'));
                $this->line("Markets: " . count($event['markets'] ?? []));

                if (isset($event['markets'][0])) {
                    $market = $event['markets'][0];
                    $this->line("Sample market: " . ($market['name'] ?? 'Unknown'));
                    $this->line("Selections: " . count($market['selections'] ?? []));
                }
            }
        } else {
            $this->warn("❌ Betano API returned no data");
        }

        $this->newLine();

        // Test Bet365 API
        $this->info("Testing Bet365 API...");
        $bet365Odds = $this->bet365Client->getSoccerOdds([
            'EventGroupID' => '100100', // Premier League
            'MarketID' => '75'
        ]);

        if (!empty($bet365Odds)) {
            $this->info("✅ Bet365 API: Response received");
            $this->line("Response keys: " . implode(', ', array_keys($bet365Odds)));
        } else {
            $this->warn("❌ Bet365 API returned no data");
        }

        return 0;
    }


}
