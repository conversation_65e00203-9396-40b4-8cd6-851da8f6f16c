<?php

namespace App\Console\Commands;

use App\Helpers\Utils;
use App\Services\JogosHoje\MeiliSearchService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Validator;

class MeilisearchImport extends Command
{
    protected $signature = 'meilisearch:import {type=fixtures : Request type}';

    protected $description = 'Import fixtures, leagues and teams into Meilisearch';

    public function __construct(private readonly MeiliSearchService $service)
    {
        parent::__construct();
    }

    public function handle(): int
    {
        Validator::validate($this->arguments(), [
            'type' => 'required',
        ]);

        $this->service->handle($this->argument('type'));
        try {
            $this->service->handle($this->argument('type'));
            return self::SUCCESS;
        } catch (\Exception $e) {
            $this->error(Utils::parseExceptionErrorMessage($e));
            return self::FAILURE;
        }
    }
}
