<?php

namespace App\Console\Commands;

use App\Services\JogosHoje\BetanoFeedService;
use Illuminate\Console\Command;

class FetchBetanoOddsCommand extends Command
{
    protected $signature = 'betano:fetch-odds 
                            {--league= : Fetch odds for specific league ID}
                            {--event= : Fetch odds for specific event ID}
                            {--today : Fetch only today\'s odds}
                            {--show-raw : Show raw response data}
                            {--limit=10 : Limit number of events to display}';

    protected $description = 'Fetch odds from Betano API';

    public function __construct(
        private readonly BetanoFeedService $betanoService
    ) {
        parent::__construct();
    }

    public function handle(): int
    {
        $this->info('🎯 Fetching odds from Betano...');

        try {
            $odds = $this->getOddsBasedOnOptions();

            if (empty($odds)) {
                $this->warn('❌ No odds found');
                return self::FAILURE;
            }

            $this->displayOdds($odds);
            return self::SUCCESS;

        } catch (\Exception $e) {
            $this->error('❌ Error fetching odds: ' . $e->getMessage());
            return self::FAILURE;
        }
    }

    private function getOddsBasedOnOptions(): array
    {
        if ($eventId = $this->option('event')) {
            $this->info("📋 Fetching odds for event: {$eventId}");
            return [$this->betanoService->getEventOdds($eventId)];
        }

        if ($leagueId = $this->option('league')) {
            $this->info("🏆 Fetching odds for league: {$leagueId}");
            return $this->betanoService->getLeagueOdds($leagueId);
        }

        if ($this->option('today')) {
            $this->info("📅 Fetching today's odds");
            return $this->betanoService->getTodayOdds();
        }

        $this->info("⚽ Fetching all football odds");
        return $this->betanoService->getAllFootballOdds();
    }

    private function displayOdds(array $odds): void
    {
        $limit = (int) $this->option('limit');
        $showRaw = $this->option('show-raw');
        $count = 0;

        $this->info("✅ Found " . count($odds) . " events");
        $this->newLine();

        foreach ($odds as $event) {
            if ($count >= $limit) {
                $this->info("... (showing first {$limit} events)");
                break;
            }

            if ($showRaw) {
                $this->displayRawEvent($event);
            } else {
                $this->displayFormattedEvent($event);
            }

            $count++;
        }
    }

    private function displayFormattedEvent(array $event): void
    {
        $this->line("🏟️  <info>{$event['home_team']} vs {$event['away_team']}</info>");
        $this->line("   📊 Event ID: {$event['event_id']}");
        
        if (!empty($event['league'])) {
            $this->line("   🏆 League: {$event['league']}");
        }
        
        if (!empty($event['start_time'])) {
            $this->line("   ⏰ Start: {$event['start_time']}");
        }

        if (!empty($event['markets'])) {
            $this->line("   💰 Markets:");
            foreach ($event['markets'] as $market) {
                $this->line("      📈 {$market['market_name']}:");
                foreach ($market['outcomes'] as $outcome) {
                    $this->line("         • {$outcome['outcome_name']}: {$outcome['odd']}");
                }
            }
        }

        $this->newLine();
    }

    private function displayRawEvent(array $event): void
    {
        $this->line("📋 Raw Event Data:");
        $this->line(json_encode($event, JSON_PRETTY_PRINT));
        $this->newLine();
    }
}
