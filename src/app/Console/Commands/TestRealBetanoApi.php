<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class TestRealBetanoApi extends Command
{
    protected $signature = 'test:real-betano-api {--country=PT}';
    protected $description = 'Test real Betano API endpoints to find working ones';

    public function handle(): int
    {
        $country = strtoupper($this->option('country'));
        $this->info("🔍 Testing real Betano API endpoints for country: {$country}");
        $this->newLine();

        $endpoints = $this->getTestEndpoints($country);

        foreach ($endpoints as $name => $config) {
            $this->info("Testing: {$name}");
            $this->line("URL: {$config['url']}");
            
            try {
                $response = Http::withHeaders($config['headers'])
                    ->timeout(30)
                    ->get($config['url'], $config['params'] ?? []);

                $this->line("Status: {$response->status()}");
                
                if ($response->successful()) {
                    $data = $response->json();
                    if (!empty($data)) {
                        $this->info("✅ SUCCESS! Data received");
                        $this->line("Response keys: " . implode(', ', array_keys($data)));
                        
                        if (isset($data['events'])) {
                            $this->line("Events found: " . count($data['events']));
                        }
                        
                        // Save successful response for analysis
                        file_put_contents(
                            storage_path("logs/betano_success_{$name}.json"),
                            json_encode($data, JSON_PRETTY_PRINT)
                        );
                        
                        $this->info("Response saved to: storage/logs/betano_success_{$name}.json");
                        $this->newLine();
                        continue;
                    }
                }
                
                $this->warn("❌ Failed or empty response");
                $this->line("Body preview: " . substr($response->body(), 0, 200));
                
            } catch (\Throwable $e) {
                $this->error("❌ Exception: " . $e->getMessage());
            }
            
            $this->newLine();
        }

        return 0;
    }

    private function getTestEndpoints(string $country): array
    {
        $baseUrl = $this->getBaseUrl($country);
        
        return [
            'sports_api' => [
                'url' => $baseUrl . '/api/sport',
                'headers' => $this->getBrowserHeaders($baseUrl),
                'params' => []
            ],
            'football_events' => [
                'url' => $baseUrl . '/api/sport/football',
                'headers' => $this->getBrowserHeaders($baseUrl),
                'params' => []
            ],
            'live_events' => [
                'url' => $baseUrl . '/api/live',
                'headers' => $this->getBrowserHeaders($baseUrl),
                'params' => []
            ],
            'odds_feed' => [
                'url' => $baseUrl . '/api/odds',
                'headers' => $this->getBrowserHeaders($baseUrl),
                'params' => ['sport' => 'football']
            ],
            'public_events' => [
                'url' => $baseUrl . '/api/events',
                'headers' => $this->getBrowserHeaders($baseUrl),
                'params' => ['sport' => 'football', 'limit' => 10]
            ],
            'mobile_api' => [
                'url' => str_replace('https://', 'https://mobile.', $baseUrl) . '/api/events',
                'headers' => $this->getMobileHeaders($baseUrl),
                'params' => ['sport' => 'football']
            ],
            'original_adserve' => [
                'url' => $baseUrl . '/adserve',
                'headers' => $this->getBrowserHeaders($baseUrl),
                'params' => [
                    'type' => 'OddsComparisonFeedAndLiveEvents',
                    'lang' => strtolower($country),
                    'sport' => 'FOOT'
                ]
            ]
        ];
    }

    private function getBaseUrl(string $country): string
    {
        return match ($country) {
            'BR' => 'https://br.betano.com',
            'PT' => 'https://pt.betano.com',
            'ES' => 'https://es.betano.com',
            default => 'https://pt.betano.com',
        };
    }

    private function getBrowserHeaders(string $baseUrl): array
    {
        return [
            'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept' => 'application/json, text/plain, */*',
            'Accept-Language' => 'pt-PT,pt;q=0.9,en;q=0.8',
            'Accept-Encoding' => 'gzip, deflate, br',
            'Referer' => $baseUrl . '/',
            'Origin' => $baseUrl,
            'DNT' => '1',
            'Connection' => 'keep-alive',
            'Sec-Fetch-Dest' => 'empty',
            'Sec-Fetch-Mode' => 'cors',
            'Sec-Fetch-Site' => 'same-origin',
        ];
    }

    private function getMobileHeaders(string $baseUrl): array
    {
        return [
            'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
            'Accept' => 'application/json',
            'Accept-Language' => 'pt-PT,pt;q=0.9',
        ];
    }
}
