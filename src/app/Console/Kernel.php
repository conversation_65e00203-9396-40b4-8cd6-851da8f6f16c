<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Support\Facades\App;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        Commands\GetBroadcasters::class,
        Commands\GetCountries::class,
        Commands\GetFixtures::class,
        Commands\GetFixturesEvents::class,
        Commands\GetLeagues::class,
        Commands\GetPlayers::class,
        Commands\GetSeasons::class,
        Commands\GetStandings::class,
        Commands\GetTeams::class,
        Commands\CleanUpLogs::class,
        Commands\RemoveTrailingSlashFromBlockItems::class,
        Commands\PublishScheduledArticles::class,
        Commands\GetFixturesStatistics::class,
        Commands\GetPlayerFixtureRatings::class,
        Commands\GetVenues::class,
        Commands\CalculateDashboardStats::class,
        Commands\GetPlayerInjuries::class,
        Commands\GetTheSportsAPIIds::class,
        Commands\GetReferres::class,
        Commands\MigrationScript::class,
        Commands\FixPredictionsPrettyName::class,
        Commands\FixPredictionsStatus::class,
        Commands\CreateCacheForLastAndNextDays::class,
        Commands\FixTeamsAndLeguesWithoutLogo::class,
        Commands\PopulateRegionsAndCountriesLeaguesOrder::class,
        Commands\GetLeagueRounds::class,
        Commands\GetTeamsLogosFromTheSportsAPI::class,
        Commands\MatchTeamIdsWithTheSportsIds::class,
        Commands\GetPlayerStatistics::class,
        Commands\GetLeagueWinnersFromZeroZero::class,
        Commands\UpdateStandingsColors::class,
        Commands\UpdateStatsStandingsAndPlayersColumnsOnLeagues::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param \Illuminate\Console\Scheduling\Schedule $schedule
     *
     * @return void
     */
    protected function schedule(Schedule $schedule): void
    {
        if (App::environment('production') || App::environment('dev')) {
            $schedule->command('app:renew-dates')->daily();

            /**
             * Recommended Calls : 1 call per minute.
             */
            $schedule->command('get:fixtures live')->everyMinute()->withoutOverlapping();
            $schedule->command('get:fixtures-events live')->everyMinute()->withoutOverlapping();
            $schedule->command('get:fixtures-statistics')->everyMinute()->withoutOverlapping();
            $schedule->command('get:fixtures ended')->everyFifteenMinutes()->withoutOverlapping();
            $schedule->command('get:fixtures today')->everyTwoHours()->withoutOverlapping();
            $schedule->command('get:fixtures just-ended')->everyFiveMinutes()->withoutOverlapping();
            $schedule->command('get:fixtures next-days')->cron('0 10 */2 * *')->withoutOverlapping();

            /**
             * Recommended Calls : 1 call per hour.
             */
            $schedule->command('get:leagues')->weeklyOn(2, '8:00')->withoutOverlapping();
            $schedule->command('get:standings live')->everyTenMinutes()->withoutOverlapping();

            /**
             * Recommended Calls : 1 call per day.
             */
            $schedule->command('logs:clean')->dailyAt('04:00');
            $schedule->command('get:seasons')->monthlyOn(1)->withoutOverlapping();
            $schedule->command('get:countries')->yearlyOn(12)->withoutOverlapping();
            $schedule->command('get:broadcasters')->dailyAt('05:00')->withoutOverlapping();
            $schedule->command('get:standings daily')->dailyAt('06:00')->withoutOverlapping();
            $schedule->command('get:fixtures-events daily')->dailyAt('07:00')->withoutOverlapping();
            $schedule->command('get:league-rounds all')->dailyAt('08:00')->withoutOverlapping();
            $schedule->command('get:league-rounds current')->dailyAt('09:00')->withoutOverlapping();
            $schedule->command('meilisearch:import fixtures')->dailyAt('00:00')->withoutOverlapping();

            /**
             * Recommended Calls : 1 call per day.
             * case: run every minute to decrease job timeout (less chunk every minute)
             */
            $schedule->command('get:players')->weekly()->withoutOverlapping();
            $schedule->command('get:player-injuries')->everyFourHours()->withoutOverlapping();
            $schedule->command('update:leagues-winner')->weeklyOn(1, '8:00')->withoutOverlapping();
            $schedule->command('get:teams statistics-current')->everyThirtyMinutes()->withoutOverlapping();
            $schedule->command('get:teams')->weekly()->withoutOverlapping();

            $schedule->command('get:broadcasters-teams')->weekly()->withoutOverlapping();

            /**
             * Recommended Calls : 1 call per week.
             * case: run every minute to decrease job timeout (less chunk every minute)
             */
            $schedule->command('get:players squads')->dailyAt('03:30')->withoutOverlapping();
            $schedule->command('publish:items')->everyFiveMinutes();

            /**
             * Recommended Calls : 1 every 10 minutes.
             */
            $schedule->command('get:player-fixture-ratings')->everyTenMinutes();
            $schedule->command('get:fixture-lineups')->everyFifteenMinutes();
            $schedule->command('fix:predictions-status')->everyTenMinutes();

            $schedule->command('get:venues')->monthlyOn(2)->withoutOverlapping();
            $schedule->command('calculate:dashboard-stats')->dailyAt('04:30');
            $schedule->command('auth:clear-resets')->everyOddHour();

            $schedule->command('get:thesports-api-ids')->everyTwoHours()->withoutOverlapping();

            $schedule->command('get:fixture-analysis')->cron('0 */4 * * *');

            $schedule->command('go:live')->everyThirtyMinutes()->withoutOverlapping();

            $schedule->command('get:teams-logos next-games')->cron('0 */6 * * *');

            $schedule->command('update:has-stats-standings-and-players-columns-on-leagues')->dailyAt('08:30')->withoutOverlapping();
        }

        $schedule->command('app:calculate-resolution-score')->weeklyOn(1, '3:00');
        $schedule->command('meilisearch:import teams')->weekly()->withoutOverlapping();
        $schedule->command('meilisearch:import leagues')->weekly()->withoutOverlapping();

        $schedule->command('app:complaints-automations')->dailyAt('05:00');
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
