<?php

namespace App\Services\JogosHoje;

use App\Contracts\JogosHoje\Service\DataHandlerServiceInterface;
use App\Enums\JogosHoje\CoverageType;
use App\Http\Clients\JogosHoje\FootballHttpClient;
use App\Http\Middleware\ApiHelpers;
use App\Models\JogosHoje\{Country, Fixture, FixtureLineup, FixtureLineupTeamFormation, Player};
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixtureLineupService implements DataHandlerServiceInterface
{
    private const int FETCH_CHUNK_SIZE = 1000;
    private const string API_ENDPOINT = '/fixtures/lineups';

    public function __construct(
        private readonly FixtureLineup $model,
        private readonly FixtureLineupTeamFormation $fixtureLineupTeamFormationModel,
        private readonly Fixture $fixtureModel,
        private readonly Player $playerModel,
        private readonly FootballHttpClient $client,
        private readonly PlayerService $playerService,
        private readonly CoverageService $coverageService,
        private readonly Country $countryModel,
    ) {
    }

    public function handle(array $data): void
    {
        $this->fixtureModel->fetchFixturesWithoutLineups($data['fixture'])
            ->chunk(self::FETCH_CHUNK_SIZE, function ($fixtures) {
                /** @var Fixture[] $fixtures */
                foreach ($fixtures as $fixture) {
                    /*
                    if(!$this->coverageService->hasCoverage($fixture->league_id, CoverageType::FIXTURES_HAS_LINEUPS->value)) {
                        Log::info('Fetching lineups for the uncovered league has been skipped. Fixture ID: ' . $fixture->id);
                        continue;
                    }
                    */
                    $lineups = $this->fetchFixtureLineupFromApi($fixture->api_id);

                    $this->process($lineups, $fixture);

                    sleep(1);
                }
            });
    }

    public function process(array $lineups, Fixture $fixture)
    {
        if (count($lineups) > 1) {
            [$homeTeamLineup, $awayTeamLineup] = $lineups;

            $parsedHomeTeamLineup = $this->parseLineupApiData(
                $homeTeamLineup,
                $fixture->homeTeam->id,
                $fixture->id
            );
            $parsedAwayTeamLineup = $this->parseLineupApiData(
                $awayTeamLineup,
                $fixture->awayTeam->id,
                $fixture->id
            );

            DB::transaction(function () use ($parsedHomeTeamLineup, $parsedAwayTeamLineup, $homeTeamLineup, $awayTeamLineup) {
                $homeTeamLineupModel = $this->model->updateOrCreate([
                    'team_id' => $parsedHomeTeamLineup['team_id'],
                    'fixture_id' => $parsedHomeTeamLineup['fixture_id'],
                ], $parsedHomeTeamLineup);
                $homeTeamLineupId = $homeTeamLineupModel->id;

                $awayTeamLineupModel = $this->model->updateOrCreate([
                    'team_id' => $parsedAwayTeamLineup['team_id'],
                    'fixture_id' => $parsedAwayTeamLineup['fixture_id'],
                ], $parsedAwayTeamLineup);
                $awayTeamLineupId = $awayTeamLineupModel->id;

                $this->fixtureLineupTeamFormationModel->where('lineup_id', $homeTeamLineupId)->delete();
                $this->fixtureLineupTeamFormationModel->where('lineup_id', $awayTeamLineupId)->delete();

                $this->persistLineupTeamFormation($homeTeamLineupId, $homeTeamLineup);
                $this->persistLineupTeamFormation($awayTeamLineupId, $awayTeamLineup);
            });

            $this->fixtureModel->where('id', $fixture->id)->update(['has_lineups' => 1]);
        }
    }

    public function persistLineupTeamFormation(int $lineupId, object $teamLineup): void
    {
        $formations = [];
        $substitutions = [];

        if (isset($teamLineup->startXI)) {
            foreach ($teamLineup->startXI as $lineup) {
                $lineupFormation = $this->parseLineupFormationData($lineupId, $lineup);
                if (!empty($lineupFormation)) {
                    $formations[] = $lineupFormation;
                }
            }
        }

        if (isset($teamLineup->substitutes)) {
            foreach ($teamLineup->substitutes as $lineup) {
                $lineupFormation = $this->parseLineupFormationData($lineupId, $lineup, true);
                if (!empty($lineupFormation)) {
                    $substitutions[] = $lineupFormation;
                }
            }
        }

        $this->fixtureLineupTeamFormationModel->insert($formations);
        $this->fixtureLineupTeamFormationModel->insert($substitutions);
    }

    public function parseLineupFormationData(int $lineupId, $lineup, bool $isSubstitution = false): array
    {
        if (empty($lineup->player->id)) {
            return [];
        }

        $playerId = $this->playerModel->whereApiId($lineup->player->id)->pluck('id')->first();

        if (!$playerId) {
            if (empty($this->playerService->fetchPlayerInfo($lineup->player->id))) {
                return [];
            }

            $player = $this->playerService->fetchPlayerInfo($lineup->player->id)[0]->player;
            $playerId = $this->playerModel->create([
                'api_id' => $player->id,
                'name' => $player->name,
                'firstname' => ApiHelpers::issetCheck($player, ['firstname']),
                'lastname' => ApiHelpers::issetCheck($player, ['lastname']),
                'height' => ApiHelpers::issetCheck($player, ['height']),
                'weight' => ApiHelpers::issetCheck($player, ['weight']),
                'injured' => ApiHelpers::issetCheck($player, ['injured']),
                'birth_date' => $this->playerService->getPlayerBirthDate($player),
                'birth_place' => ApiHelpers::issetCheck($player, ['birth', 'place'], null),
                'birth_country' => isset($player->birth) ? $this->countryModel->getCountryIdByName(
                    $player->birth->country
                ) : null,
                'updated_at' => now()->format('Y-m-d H:i:s'),
            ])->id;
        }

        return [
            'lineup_id' => $lineupId,
            'player_id' => $playerId,
            'player_number' => $lineup->player->number,
            'player_position' => $lineup->player->pos,
            'grid' => $lineup->player->grid,
            'is_substitution' => $isSubstitution,
        ];
    }

    public function fetchFixtureLineupFromApi(string $apiId): array
    {
        return $this->client->get(self::API_ENDPOINT, ['fixture' => $apiId]);
    }

    public function parseLineupApiData(object $lineup, int $teamId, int $fixtureId): array
    {
        return [
            'formation' => $lineup->formation,
            'team_id' => $teamId,
            'fixture_id' => $fixtureId,
            'coach_name' => $lineup->coach->name ?? null,
            'coach_photo' => $lineup->coach->photo ?? null,
        ];
    }

    public function fixtureLineup(int $fixtureId): Collection
    {
        return $this->model
            ->where('fixture_id', $fixtureId)
            ->with('teamFormation.player')
            ->get();
    }

    public function findTeamPreviousLineup(int $teamId, int $leagueId): Collection
    {
        return $this->model->findTeamPreviousLineup($teamId, $leagueId)->get();
    }

    public function checkPreviousLineups(Fixture $fixture): void
    {
        if ($fixture->has_lineups === false) {
            $homeTeamProbableLineup = $this->findTeamPreviousLineup(
                $fixture->homeTeam->id,
                $fixture->league_id
            );
            $awayTeamProbableLineup = $this->findTeamPreviousLineup(
                $fixture->awayTeam->id,
                $fixture->league_id
            );

            $lineups = $homeTeamProbableLineup->merge($awayTeamProbableLineup);
            if ($lineups->isNotEmpty()) {
                $fixture->has_lineups = true;
            }
        }
    }
}
