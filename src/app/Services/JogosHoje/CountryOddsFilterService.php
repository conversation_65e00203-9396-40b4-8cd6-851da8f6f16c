<?php

namespace App\Services\JogosHoje;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class CountryOddsFilterService
{
    /**
     * Mapping of countries to allowed bookmakers
     */
    private array $allowedBookmakers = [
        'PT' => ['Betano', 'Bet365'], // Portugal
        'BR' => ['Betano', 'Bet365'], // Brazil
        'ES' => ['Betano', 'Bet365'], // Spain
        'IT' => ['Betano'],           // Italy
        'DE' => ['Bet365'],           // Germany
        'UK' => ['Bet365'],           // United Kingdom
        'FR' => [],                   // France (restricted)
        // Add more countries as needed
    ];

    /**
     * Default country when detection fails
     */
    private string $defaultCountry = 'PT';

    /**
     * Get allowed bookmakers for a country
     */
    public function getAllowedBookmakers(string $countryCode): array
    {
        return $this->allowedBookmakers[$countryCode] ?? $this->allowedBookmakers[$this->defaultCountry];
    }

    /**
     * Get user's country from request (set by GeoIP middleware)
     */
    public function getUserCountry(Request $request): string
    {
        $country = $request->get('countryIsoCode2', $this->defaultCountry);
        
        Log::debug('Country detection for odds filtering', [
            'detected_country' => $country,
            'user_ip' => $request->ip(),
            'allowed_bookmakers' => $this->getAllowedBookmakers($country)
        ]);

        return $country;
    }

    /**
     * Filter odds array based on user's country
     */
    public function filterOddsByCountry(array $odds, string $countryCode): array
    {
        $allowedBookmakers = $this->getAllowedBookmakers($countryCode);
        
        if (empty($allowedBookmakers)) {
            Log::warning("No bookmakers allowed for country: {$countryCode}");
            return [];
        }

        $filteredOdds = [];

        foreach ($odds as $oddsEntry) {
            if (!isset($oddsEntry->bookmakers)) {
                continue;
            }

            $filteredBookmakers = [];

            foreach ($oddsEntry->bookmakers as $bookmaker) {
                $bookmakerName = $bookmaker->name ?? '';
                
                if (in_array($bookmakerName, $allowedBookmakers)) {
                    $filteredBookmakers[] = $bookmaker;
                }
            }

            if (!empty($filteredBookmakers)) {
                $filteredEntry = clone $oddsEntry;
                $filteredEntry->bookmakers = $filteredBookmakers;
                $filteredOdds[] = $filteredEntry;
            }
        }

        Log::debug('Odds filtered by country', [
            'country' => $countryCode,
            'original_odds_count' => count($odds),
            'filtered_odds_count' => count($filteredOdds),
            'allowed_bookmakers' => $allowedBookmakers
        ]);

        return $filteredOdds;
    }

    /**
     * Check if a bookmaker is allowed in a country
     */
    public function isBookmakerAllowed(string $bookmakerName, string $countryCode): bool
    {
        $allowedBookmakers = $this->getAllowedBookmakers($countryCode);
        return in_array($bookmakerName, $allowedBookmakers);
    }

    /**
     * Get all supported countries
     */
    public function getSupportedCountries(): array
    {
        return array_keys($this->allowedBookmakers);
    }

    /**
     * Add or update bookmakers for a country
     */
    public function setBookmakersForCountry(string $countryCode, array $bookmakers): void
    {
        $this->allowedBookmakers[$countryCode] = $bookmakers;
    }

    /**
     * Get country-specific message for restricted access
     */
    public function getRestrictedMessage(string $countryCode): string
    {
        $allowedBookmakers = $this->getAllowedBookmakers($countryCode);
        
        if (empty($allowedBookmakers)) {
            return "Betting services are not available in your country ({$countryCode}).";
        }

        $bookmakersList = implode(', ', $allowedBookmakers);
        return "Available bookmakers in your country ({$countryCode}): {$bookmakersList}";
    }
}
