<?php

namespace App\Services\JogosHoje;

use App\Models\JogosHoje\{Fixture, Statistic, StatisticDetails};
use App\Models\Odd;
use Illuminate\Support\Collection;
use App\Http\Clients\JogosHoje\FootballHttpClient;
use App\Services\JogosHoje\BetometerStreaksService;

class ValueBetService
{
    public const MARKETS = [1, 3, 5, 8, 14, 45, 80];
    public const BOOKMAKERS = [8, 32, 34];

    // Mapping between API market IDs and streak market IDs
    public const MARKET_MAPPING = [
        1 => 1,   // 1X2 WIN -> MARKET_ID 1
        3 => 4,   // 1ST HALF RESULT -> MARKET_ID 4
        5 => 4,   // TOTAL GOALS O/U -> MARKET_ID 4
        8 => 4,   // BTTS (YES/NO) -> MARKET_ID 4
        14 => 4,  // 1ST TO SCORE -> MARKET_ID 4
        45 => 3,  // TOTAL CORNERS O/U -> MARKET_ID 3
        80 => 2,  // CARD O/U -> MARKET_ID 2
    ];

    public function __construct(
        private readonly BetometerStreaksService $streaksService,
        private readonly FootballHttpClient $httpClient,
        private readonly Statistic $statisticModel,
        private readonly StatisticDetails $statisticDetailsModel,
    ) {}

    public function getValueBetsForFixture(int $fixtureId): Collection
    {
        return Odd::where('fixture_id', $fixtureId)
            ->whereIn('market_id', self::MARKETS)
            ->whereIn('bookmaker_id', self::BOOKMAKERS)
            ->where(function ($q) {
                $q->where('is_hot_bet', true)
                    ->orWhere('is_trending_bet', true);
            })
            ->get();
    }

    public function fetchRawOddsForFixture(Fixture $fixture): mixed
    {
        $data = $this->httpClient->get('/odds', [
            'fixture' => $fixture->api_id,
        ]);

        if (!is_iterable($data)) {
            return $data;
        }

        $filtered = [];
        foreach ($data as $entry) {
            $bookmakers = [];
            foreach ($entry->bookmakers ?? [] as $bookmaker) {
                if (!in_array($bookmaker->id, self::BOOKMAKERS)) {
                    continue;
                }

                $bets = [];
                foreach ($bookmaker->bets ?? [] as $bet) {
                    $bets[] = $bet;
                }

                if (!empty($bets)) {
                    $bookmaker->bets = $bets;
                    $bookmakers[] = $bookmaker;
                }
            }

            if (!empty($bookmakers)) {
                $entry->bookmakers = $bookmakers;
                $filtered[] = $entry;
            }
        }

        return $filtered;
    }

    public function handleFixture(Fixture $fixture): void
    {
        $data = $this->fetchRawOddsForFixture($fixture);

        if (!is_iterable($data)) {
            return;
        }

        $this->streaksService->handle([
            'homeTeamId' => $fixture->team_home,
            'awayTeamId' => $fixture->team_away,
        ]);

        $homeStreaks  = collect($this->streaksService->result['home_team'] ?? []);
        $awayStreaks  = collect($this->streaksService->result['away_team'] ?? []);
        $hasStreak    = $homeStreaks->isNotEmpty() || $awayStreaks->isNotEmpty();
        $combinedStreak = $homeStreaks->isNotEmpty() && $awayStreaks->isNotEmpty();

        $streakTypes = $homeStreaks->merge($awayStreaks)
            ->pluck('statistic_type')
            ->filter()
            ->unique()
            ->all();
        $hasSingleType = in_array('single', $streakTypes, true);

        $maxOdds = [];

        foreach ($data as $entry) {
            if (empty($entry->bookmakers)) {
                continue;
            }

            foreach ($entry->bookmakers as $bookmaker) {
                if (!in_array($bookmaker->id, self::BOOKMAKERS) || empty($bookmaker->bets)) {
                    continue;
                }

                foreach ($bookmaker->bets as $bet) {
                    if (empty($bet->values)) {
                        continue;
                    }

                    foreach ($bet->values as $value) {
                        $oddValue = (float) $value->odd;

                        if (!isset($maxOdds[$bet->id][$value->value]) || $oddValue > $maxOdds[$bet->id][$value->value]['odd']) {
                            $maxOdds[$bet->id][$value->value] = [
                                'odd'          => $oddValue,
                                'bookmaker_id' => $bookmaker->id,
                            ];
                        }
                    }
                }
            }
        }
        foreach ($maxOdds as $marketId => $options) {
            foreach ($options as $optionName => $dataOdd) {
                $oddValue        = $dataOdd['odd'];
                $statisticData   = $this->getStatisticData($fixture, $marketId);
                $true            = $statisticData['probability'];
                $apiProb         = 1 / $oddValue;
                $fairOdd         = $true > 0 ? 1 / $true : 0.0;
                $expected        = ($true * $oddValue) - 1;
                $evPositive = $expected > 0;

                $isTrending = $evPositive && $hasSingleType;
                $isHot      = $evPositive && !$hasSingleType && $combinedStreak;

                if (!$isHot && !$isTrending) {
                    continue; // discard non value bet
                }

                Odd::updateOrCreate(
                    [
                        'fixture_id'   => $fixture->id,
                        'home_team_id' => $fixture->team_home,
                        'away_team_id' => $fixture->team_away,
                        'market_id'    => $marketId,
                        'option_name'  => $optionName,
                    ],
                    [
                        'bookmaker_id'    => $dataOdd['bookmaker_id'],
                        'odd_value'       => $oddValue,
                        'true_probability'=> $true,
                        'api_probability' => $apiProb,
                        'expected_value'  => round($expected, 4),
                        'ev_positive'     => $evPositive,
                        'is_hot_bet'      => $isHot,
                        'is_trending_bet' => $isTrending,
                    ]
                );
            }
        }
    }


    public function calculateTrueProbability(Fixture $fixture, int $marketId): float
    {
        return $this->getStatisticData($fixture, $marketId)['probability'];
    }

    public function getMax1X2Odd(Fixture $fixture, string $prettyName): float
    {
        return $this->getMaxOddForMarket($fixture, 1);
    }

    public function getMaxOddForMarket(Fixture $fixture, int $marketId): float
    {
        $odd = Odd::where('fixture_id', $fixture->id)
            ->where('market_id', $marketId)
            ->orderByDesc('odd_value')
            ->first();

        return $odd?->odd_value ?? 0.0;
    }

    public function getMarketName(int $marketId): string
    {
        return match ($marketId) {
            1 => '1X2',
            2 => 'Cards',
            3 => 'Corners',
            4 => 'Goals',
            5 => 'Total Goals O/U',
            8 => 'BTTS',
            14 => '1st To Score',
            45 => 'Total Corners O/U',
            80 => 'Cards O/U',
            default => 'Other',
        };
    }

    private function getStatisticData(Fixture $fixture, int $marketId): array
    {
        $statistic = $this->statisticModel
            ->where('fixture_id', $fixture->id)
            ->where('market_id', $marketId)
            ->first();

        if (!$statistic) {
            return ['probability' => 0.0, 'type' => null, 'config_pretty_name' => null, 'pretty_name' => null];
        }

        $details = [];
        if ($statistic->statistic_details_1_id) {
            $details[] = $this->statisticDetailsModel->find($statistic->statistic_details_1_id);
        }
        if ($statistic->statistic_details_2_id) {
            $details[] = $this->statisticDetailsModel->find($statistic->statistic_details_2_id);
        }

        $probs = [];
        foreach ($details as $detail) {
            if ($detail && (float) $detail->count > 0) {
                $probs[] = $detail->value / $detail->count;
            }
        }
        $probability = empty($probs) ? 0.0 : round(array_sum($probs) / count($probs), 4);

        return [
            'probability'        => $probability,
            'type'               => $statistic->type,
            'config_pretty_name' => $details[0]->config_pretty_name ?? null,
            'pretty_name'        => $statistic->pretty_name,
        ];
    }

    public function getStatisticType(Fixture $fixture, int $marketId): ?string
    {
        return $this->getStatisticData($fixture, $marketId)['type'];
    }
}
