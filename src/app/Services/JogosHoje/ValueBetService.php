<?php

namespace App\Services\JogosHoje;

use App\Models\JogosHoje\{Fixture, StatisticDetails};
use App\Models\Odd;
use Illuminate\Support\Collection;
use App\Http\Clients\JogosHoje\FootballHttpClient;
use App\Services\JogosHoje\BetometerStreaksService;

class ValueBetService
{
    public const MARKETS = [1, 3, 6, 8, 10, 11, 5];
    public const BOOKMAKERS = [8, 32, 34];

    public function __construct(
        private readonly BetometerStreaksService $streaksService,
        private readonly FootballHttpClient $httpClient
    ) {}

    public function getValueBetsForFixture(int $fixtureId): Collection
    {
        return Odd::where('fixture_id', $fixtureId)
            ->whereIn('market_id', self::MARKETS)
            ->whereIn('bookmaker_id', self::BOOKMAKERS)
            ->where(function ($q) {
                $q->where('is_hot_bet', true)
                    ->orWhere('is_trending_bet', true);
            })
            ->get();
    }

    public function fetchRawOddsForFixture(Fixture $fixture): mixed
    {
        $data = $this->httpClient->get('/odds', [
            'fixture' => $fixture->api_id,
        ]);

        if (!is_iterable($data)) {
            return $data;
        }

        $filtered = [];
        foreach ($data as $entry) {
            $bookmakers = [];
            foreach ($entry->bookmakers ?? [] as $bookmaker) {
                if (!in_array($bookmaker->id, self::BOOKMAKERS)) {
                    continue;
                }

                $bets = [];
                foreach ($bookmaker->bets ?? [] as $bet) {
                    $bets[] = $bet;
                }

                if (!empty($bets)) {
                    $bookmaker->bets = $bets;
                    $bookmakers[] = $bookmaker;
                }
            }

            if (!empty($bookmakers)) {
                $entry->bookmakers = $bookmakers;
                $filtered[] = $entry;
            }
        }

        return $filtered;
    }

    public function handleFixture(Fixture $fixture): void
    {
        $data = $this->fetchRawOddsForFixture($fixture);

        if (!is_iterable($data)) {
            return;
        }

        $this->streaksService->handle([
            'homeTeamId' => $fixture->team_home,
            'awayTeamId' => $fixture->team_away,
        ]);

        $hasStreak = !empty($this->streaksService->result['home_team'])
            || !empty($this->streaksService->result['away_team']);
        $combinedStreak = !empty($this->streaksService->result['home_team'])
            && !empty($this->streaksService->result['away_team']);

        foreach ($data as $entry) {
            if (empty($entry->bookmakers)) {
                continue;
            }

            foreach ($entry->bookmakers as $bookmaker) {
                if (!in_array($bookmaker->id, self::BOOKMAKERS) || empty($bookmaker->bets)) {
                    continue;
                }

                foreach ($bookmaker->bets as $bet) {
                    if (empty($bet->values)) {
                        continue;
                    }

                    foreach ($bet->values as $value) {
                        $oddValue   = (float) $value->odd;
                        $true       = $this->calculateTrueProbability($fixture, $bet->id);
                        $apiProb    = 1 / $oddValue;
                        $evPositive = $true > $apiProb;

                        $isHot      = $evPositive && $combinedStreak;
                        $isTrending = $evPositive && !$combinedStreak && $hasStreak;

                        if (!$isHot && !$isTrending) {
                            continue; // discard non value bet
                        }

                        Odd::updateOrCreate(
                            [
                                'fixture_id'   => $fixture->id,
                                'home_team_id' => $fixture->team_home,
                                'away_team_id' => $fixture->team_away,
                                'market_id'    => $bet->id,
                                'bookmaker_id' => $bookmaker->id,
                            ],
                            [
                                'odd_value'        => $oddValue,
                                'true_probability' => $true,
                                'api_probability'  => $apiProb,
                                'ev_positive'      => $evPositive,
                                'is_hot_bet'       => $isHot,
                                'is_trending_bet'  => $isTrending,
                            ]
                        );
                    }
                }
            }
        }
    }


    public function calculateTrueProbability(Fixture $fixture, int $marketId): float
    {
        // Streak information currently only exists for the 1X2 market.
        // Return zero probability for any other market.
        if ($marketId !== 1) {
            return 0.0;
        }

        $home = $this->teamStat($fixture, 'wins', true);
        $away = $this->teamStat($fixture, 'defeats', false);

        return round(($home + $away) / 2, 4);
    }

    private function teamStat(Fixture $fixture, string $key, bool $home): float
    {
       $teamId = $home ? $fixture->team_home : $fixture->team_away;

            $stat = StatisticDetails::query()
            ->where('fixture_id', $fixture->id)
            ->where('team_id', $teamId)
            ->where('config_type_id', $key)
            ->first();

        if ($stat && (float) $stat->count > 0) {
            return round($stat->value / $stat->count, 4);
        }

        return 0.0;
    }
}
