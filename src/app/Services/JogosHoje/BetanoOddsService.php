<?php

namespace App\Services\JogosHoje;

use App\Http\Clients\Bookmakers\BetanoHttpClient;
use App\Models\Odd;
use App\Models\JogosHoje\Fixture;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class BetanoOddsService
{
    public function __construct(
        private readonly BetanoHttpClient $betanoClient
    ) {}

    /**
     * Fetch and save odds from Betano for all available matches
     */
    public function fetchAndSaveAllOdds(string $region = 'PT'): array
    {
        $this->betanoClient->setCountry($region);
        
        Log::info('[BETANO_ODDS_SERVICE] Starting to fetch all odds', ['region' => $region]);
        
        $data = $this->betanoClient->getCombinedOdds();
        
        if (empty($data['events'])) {
            Log::warning('[BETANO_ODDS_SERVICE] No events found in Betano response');
            return [];
        }

        $processedEvents = [];
        
        foreach ($data['events'] as $event) {
            try {
                $result = $this->processEventData($event, $region);
                if ($result) {
                    $processedEvents[] = $result;
                }
            } catch (\Throwable $e) {
                Log::error('[BETANO_ODDS_SERVICE] Error processing event: ' . $e->getMessage(), [
                    'event_id' => $event['id'] ?? 'unknown'
                ]);
            }
        }

        Log::info('[BETANO_ODDS_SERVICE] Finished processing events', [
            'region' => $region,
            'total_events' => count($data['events']),
            'processed_events' => count($processedEvents)
        ]);

        return $processedEvents;
    }

    /**
     * Fetch odds for a specific event by ID
     */
    public function fetchOddsForEvent(string $eventId, string $region = 'PT'): array
    {
        $this->betanoClient->setCountry($region);
        
        Log::info('[BETANO_ODDS_SERVICE] Fetching odds for specific event', [
            'event_id' => $eventId,
            'region' => $region
        ]);
        
        $data = $this->betanoClient->getOddsByEventId($eventId);
        
        if (empty($data['events'])) {
            Log::warning('[BETANO_ODDS_SERVICE] No data found for event', ['event_id' => $eventId]);
            return [];
        }

        $event = $data['events'][0] ?? null;
        if (!$event) {
            return [];
        }

        return $this->processEventData($event, $region) ?? [];
    }

    /**
     * Process individual event data and save to database
     */
    private function processEventData(array $event, string $region): ?array
    {
        $eventId = $event['id'] ?? null;
        $eventName = $event['name'] ?? 'Unknown Match';
        
        if (!$eventId) {
            Log::warning('[BETANO_ODDS_SERVICE] Event missing ID', ['event_name' => $eventName]);
            return null;
        }

        // Try to find matching fixture in our database
        $fixture = $this->findMatchingFixture($event);
        
        if (!$fixture) {
            Log::debug('[BETANO_ODDS_SERVICE] No matching fixture found', [
                'betano_event_id' => $eventId,
                'event_name' => $eventName
            ]);
            // Still process the event for potential future matching
        }

        $processedMarkets = [];
        
        foreach ($event['markets'] as $market) {
            try {
                $savedOdds = $this->saveMarketOdds($market, $fixture, $eventId, $region);
                if ($savedOdds) {
                    $processedMarkets[] = $savedOdds;
                }
            } catch (\Throwable $e) {
                Log::error('[BETANO_ODDS_SERVICE] Error saving market odds: ' . $e->getMessage(), [
                    'event_id' => $eventId,
                    'market_name' => $market['name'] ?? 'unknown'
                ]);
            }
        }

        return [
            'betano_event_id' => $eventId,
            'event_name' => $eventName,
            'fixture_id' => $fixture?->id,
            'markets_processed' => count($processedMarkets),
            'markets' => $processedMarkets
        ];
    }

    /**
     * Find matching fixture in our database
     */
    private function findMatchingFixture(array $event): ?Fixture
    {
        $eventName = $event['name'] ?? '';
        
        // Extract team names from "Team A vs Team B" format
        if (str_contains($eventName, ' vs ')) {
            [$homeTeam, $awayTeam] = explode(' vs ', $eventName, 2);
            
            // Try to find fixture by team names (fuzzy matching)
            $fixture = Fixture::whereHas('homeTeam', function($q) use ($homeTeam) {
                $q->where('name', 'LIKE', '%' . trim($homeTeam) . '%');
            })->whereHas('awayTeam', function($q) use ($awayTeam) {
                $q->where('name', 'LIKE', '%' . trim($awayTeam) . '%');
            })->first();
            
            if ($fixture) {
                return $fixture;
            }
        }

        // If no exact match, try to find by Betano event ID if we have it stored
        // This would require a betano_event_id field in fixtures table
        
        return null;
    }

    /**
     * Save market odds to database
     */
    private function saveMarketOdds(array $market, ?Fixture $fixture, string $eventId, string $region): ?array
    {
        $marketName = $market['name'] ?? '';
        $marketId = $market['id'] ?? 1;
        $selections = $market['selections'] ?? [];
        
        if (empty($selections)) {
            return null;
        }

        // Prepare odds data based on market type
        $oddsData = $this->prepareOddsData($marketName, $selections);
        
        if (empty($oddsData)) {
            return null;
        }

        // Save to database
        $oddRecord = Odd::updateOrCreate([
            'fixture_id' => $fixture?->id,
            'betano_event_id' => $eventId,
            'market_id' => $marketId,
            'bookmaker_id' => 1001, // Betano ID
            'region' => $region
        ], array_merge($oddsData, [
            'updated_at' => now()
        ]));

        Log::debug('[BETANO_ODDS_SERVICE] Saved odds', [
            'fixture_id' => $fixture?->id,
            'betano_event_id' => $eventId,
            'market' => $marketName,
            'region' => $region
        ]);

        return [
            'market_name' => $marketName,
            'market_id' => $marketId,
            'selections_count' => count($selections),
            'odds_data' => $oddsData
        ];
    }

    /**
     * Prepare odds data based on market type
     */
    private function prepareOddsData(string $marketName, array $selections): array
    {
        $oddsData = [];
        
        switch ($marketName) {
            case '1X2':
                foreach ($selections as $selection) {
                    $name = $selection['name'] ?? '';
                    $odds = $selection['odds'] ?? 0;
                    
                    if ($name === 'Home' || $name === '1') {
                        $oddsData['odd_home'] = $odds;
                    } elseif ($name === 'Draw' || $name === 'X') {
                        $oddsData['odd_draw'] = $odds;
                    } elseif ($name === 'Away' || $name === '2') {
                        $oddsData['odd_away'] = $odds;
                    }
                }
                break;
                
            case 'Double Chance':
                foreach ($selections as $selection) {
                    $name = $selection['name'] ?? '';
                    $odds = $selection['odds'] ?? 0;
                    
                    if ($name === 'Home/Draw' || $name === '1X') {
                        $oddsData['odd_home_draw'] = $odds;
                    } elseif ($name === 'Draw/Away' || $name === 'X2') {
                        $oddsData['odd_draw_away'] = $odds;
                    } elseif ($name === 'Home/Away' || $name === '12') {
                        $oddsData['odd_home_away'] = $odds;
                    }
                }
                break;
                
            case 'Goals Over/Under':
                foreach ($selections as $selection) {
                    $name = $selection['name'] ?? '';
                    $odds = $selection['odds'] ?? 0;
                    
                    if (str_contains($name, 'Over') || str_contains($name, 'Mais')) {
                        $oddsData['odd_over'] = $odds;
                    } elseif (str_contains($name, 'Under') || str_contains($name, 'Menos')) {
                        $oddsData['odd_under'] = $odds;
                    }
                }
                break;
                
            case 'Both Teams to Score':
                foreach ($selections as $selection) {
                    $name = $selection['name'] ?? '';
                    $odds = $selection['odds'] ?? 0;
                    
                    if ($name === 'Yes' || $name === 'Sim') {
                        $oddsData['odd_btts_yes'] = $odds;
                    } elseif ($name === 'No' || $name === 'Não') {
                        $oddsData['odd_btts_no'] = $odds;
                    }
                }
                break;
        }
        
        return $oddsData;
    }

    /**
     * Get cached odds for a fixture
     */
    public function getCachedOddsForFixture(int $fixtureId, string $region = 'PT'): ?array
    {
        $cacheKey = "betano_odds_fixture_{$fixtureId}_{$region}";
        return Cache::get($cacheKey);
    }

    /**
     * Cache odds for a fixture
     */
    public function cacheOddsForFixture(int $fixtureId, array $odds, string $region = 'PT'): void
    {
        $cacheKey = "betano_odds_fixture_{$fixtureId}_{$region}";
        Cache::put($cacheKey, $odds, now()->addMinutes(5));
    }
}
