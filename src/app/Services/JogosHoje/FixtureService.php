<?php

namespace App\Services\JogosHoje;

use App\Contracts\JogosHoje\Service\ServiceInterface;
use App\Http\Clients\JogosHoje\FootballHttpClient;
use App\Models\JogosHoje\{Fixture as FixtureModel, FixturePrediction, League, LeagueSeason, Referee, Season, Standing, Team, Venue};
use App\Traits\RapidApiTranslation;
use Barryvdh\Debugbar\Facades\Debugbar;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Collection as SupportCollection;
use Illuminate\Support\Facades\{Cache, DB, Log};
use Illuminate\Http\Request;

class FixtureService implements ServiceInterface
{
    use RapidApiTranslation;

    public const string API_ENDPOINT = '/fixtures';

    public const string API_FIXTURES_ROUNDS_ENDPOINT = '/fixtures/rounds';

    private const float PROBABILITIES_THRESHOLD = 0.5;

    public function __construct(
        private readonly FixtureModel $model,
        private readonly Team $teamModel,
        private readonly League $leagueModel,
        private readonly FootballHttpClient $footballHttpClient,
        private readonly Venue $venueModel,
        private readonly Referee $refereeModel,
        private readonly LeagueSeason $leagueSeasonModel,
        private readonly Season $seasonModel,
        private readonly RefereesService $refereesService,
        private readonly FixturePrediction $fixturePredictionModel,
        private readonly PlayerService $playerService,
        private readonly Standing $standingModel,
    ) {
    }

    public function handle(?string $type = null): void
    {
        $fixtures = $this->fetchFixturesByType($type);
        $this->process($fixtures);

        if ($type === 'ended') {
            $this->endWrongLiveFixtures($fixtures);
        }
    }

    public function fetchFixturesByType(string $type, array $params = ['live' => 'all']): array
    {
        return match ($type) {
            'live' => $this->fetchApiFixtures($params),
            'all-seasons' => $this->fetchFixturesForSeasons(),
            'today' => $this->fetchTodayFixtures(),
            'just-ended' => $this->fetchJustEnded(),
            'next-days' => $this->fetchNextDaysFixtures(),
            'all' => $this->fetchAll(),
            'custom' => $this->fetchApiFixtures(['from' => '2025-04-14', 'to' => '2025-04-16']),
            'between-dates' => $this->fetchFixturesBetweenDates(),
            default => $this->fetchEndedFixtures(),
        };
    }

    public function fetchAll(): array
    {
        $leagues = $this->leagueModel
            ->where('in_use', 1)
            ->orderBy('id')
            ->get();

        $seasons = $this
            ->seasonModel
            ->orderBy('season', 'desc')
            ->get();

        foreach ($seasons as $season) {
            foreach ($leagues as $league) {
                $fixtures = $this->fetchApiFixtures([
                    'league' => $league->api_id,
                    'season' => $season->season,
                ]);

                if (sizeof($fixtures)) {
                    $this->process($fixtures);
                }

                usleep(100);
            }
        }

        return [];
    }

    public function fetchNextDaysFixtures(): array
    {
        $now = Carbon::now()->format('Y-m-d');
        $later = Carbon::now()->addDays(15)->format('Y-m-d');

        $leaguesSeasons = $this->leagueSeasonModel->currentLeagues()->get();

        $apiData = [];
        foreach ($leaguesSeasons as $leaguesSeason) {
            $params = [
                'league' => $leaguesSeason->league,
                'season' => $leaguesSeason->season,
                'from' => $now,
                'to' => $later,
            ];
            dump($params);
            $response = $this->fetchApiFixtures($params);

            $apiData = [...$apiData, ...$response];

            usleep(100);
        }

        return $apiData;
    }

    public function fetchApiFixtures(array $query): array
    {
        return $this->footballHttpClient->get(self::API_ENDPOINT, $query);
    }

    public function fetchApiFixturesRounds(array $query): array
    {
        return $this->footballHttpClient->get(self::API_FIXTURES_ROUNDS_ENDPOINT, $query);
    }

    public function process(array $fixtures): void
    {
        $parsedFixtureData = $this->prepareData($fixtures);
        $this->model->persistFixtures($parsedFixtureData);
    }

    public static function getTranslationData(): mixed
    {
        return SearchReplaceService::getFileData('/rounds-unique.json');
    }

    public function prepareData(array $fixtures): array
    {
        $data = [];

        foreach ($fixtures as $item) {
            $teamHomeId = $this->teamModel->findByApiId($item->teams->home->id)?->pluck('id')->first();
            $teamAwayId = $this->teamModel->findByApiId($item->teams->away->id)?->pluck('id')->first();
            $league = $this->leagueModel->findByApiId($item->league->id)?->first();
            $venueId = $this->venueModel->where('api_id', $item->fixture->venue->id)?->pluck('id')->first();
            $seasonId = $this->seasonModel->where('season', $item->league->season)?->pluck('id')->first();

            if (empty($teamAwayId) || empty($teamHomeId) || empty($league->id)) {
                continue;
            }

            if (!$league->in_use) {
                continue;
            }

            $winner = $item->teams->home->winner ? 'H' : ($item->teams->away->winner ? 'A' : null);

            $referee = $this->refereesService->save($item->fixture->referee, $league->id);

            $cards_home = 0;
            $cards_away = 0;
            $corners_home = 0;
            $corners_away = 0;

            if (isset($item->statistics)) {
                foreach ($item->statistics as $index => $statistic) {
                    foreach ($statistic->statistics as $entry) {
                        switch ($entry->type) {
                            case 'Yellow Cards':
                            case 'Red Cards': {
                                if ($index === 0) {
                                    $cards_home += (int) $entry->value;
                                } else {
                                    $cards_away += (int) $entry->value;
                                }
                                break;
                            }
                            case 'Corner Kicks': {
                                if ($index === 0) {
                                    $corners_home += (int) $entry->value;
                                } else {
                                    $corners_away += (int) $entry->value;
                                }
                                break;
                            }
                            default:
                                break;
                        }
                    }
                }
            }

            $isLive = $this->model::isLive($item->fixture->status->short) && $this->between3HoursAndAHalf($item->fixture->timestamp);

            $fixtureArr = [
                'api_id' => $item->fixture->id,
                'date' => date('Y-m-d H:i:s', $item->fixture->timestamp),
                'league_id' => $league->id,
                'round' => self::translateRound($item->league->round),
                'team_home' => $teamHomeId,
                'team_away' => $teamAwayId,
                'status' => $isLive ? $item->fixture->status->short : ($item->fixture->status->short == '2H' ? 'FT' : $item->fixture->status->short),
                'elapsed' => $item->fixture->status->elapsed,
                'is_live' => $isLive,
                'winner' => $winner,
                'goals_home' => $item->goals->home,
                'goals_away' => $item->goals->away,
                'goals_extra_home' => $item->score->extratime->home,
                'goals_extra_away' => $item->score->extratime->away,
                'penalty_home' => $item->score->penalty->home,
                'penalty_away' => $item->score->penalty->away,
                'last_updated' => now()->format('Y-m-d H:i:s'),
                'no_stadings' => 0,
                'venue_id' => $venueId,
                'referee_id' => $referee->id ?? null,
                'season_id' => $seasonId,
                'cards_home' => $cards_home,
                'cards_away' => $cards_away,
                'corners_home' => $corners_home,
                'corners_away' => $corners_away,
                'goals_home_ht' => $item->score->halftime->home ?? 0,
                'goals_away_ht' => $item->score->halftime->away ?? 0,
                '_round' => $item->league->round,
            ];

            if ($item->fixture->status->short === '1H' && $item->fixture->status->extra) {
                $fixtureArr['extra_1H'] = $item->fixture->status->extra;
            }

            if ($item->fixture->status->short === '2H' && $item->fixture->status->extra) {
                $fixtureArr['extra_2H'] = $item->fixture->status->extra;
            }

            $data[] = $fixtureArr;
        }

        return $data;
    }

    public function fetchFixturesForSeasons(): array
    {
        $leagues = $this->leagueModel
            ->where('in_use', 1)
            ->orderBy('id')
            ->get();

        $seasons = $this
            ->seasonModel
            ->where('season', '<=', 2025)
            ->orderBy('season', 'desc')
            ->get();

        foreach ($seasons as $season) {
            foreach ($leagues as $league) {
                $fixtures = $this->model
                    ->join('league_seasons', function ($join) {
                        $join->on('league_seasons.league_id', '=', 'fixtures.league_id');
                        $join->on('league_seasons.current', '=', DB::raw("1"));
                    })
                    ->join('leagues', 'leagues.id', '=', 'fixtures.league_id')
                    ->whereIn('fixtures.status', ['FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO'])
                    ->whereRaw("fixtures.season_id = league_seasons.season_id")
                    ->where('fixtures.league_id', $league->id)
                    ->where('fixtures.season_id', $season->id)
                    ->select(['fixtures.api_id'])
                    ->get();

                $apiData = [];
                foreach ($fixtures as $fixture) {
                    Log::info('Fixture All-Seasons: ' . $season->season . ' - ' . $league->name . ' - ' . $fixture->api_id);

                    try {
                        $response = $this->fetchApiFixtures([
                            'id' => $fixture->api_id,
                        ]);

                        $apiData = [...$apiData, ...$response];
                    } catch (\Exception $e) {
                        sleep(9);
                        Log::info('Fixture All-Seasons Error (' . $fixture->api_id . '): ' . $e->getMessage());
                    }

                    sleep(1);
                }

                $this->process($apiData);
            }
        }

        return [];
    }

    public function fetchTodayFixtures(): array
    {
        $fixtures = $this->model
            ->getTodayFixturesHaventStartedYet()
            ->get();

        return $this->processApiData($fixtures);
    }

    public function fetchFixturesBetweenDates()
    {
        $fixtures = $this->model
            ->whereRaw("DATE(fixtures.date) BETWEEN '2025-05-05' AND '2025-06-01'")
            ->orderBy('id', 'asc')
            ->get();

        foreach ($fixtures as $fixture) {
            $this->validatePredictions($fixture);
        }

        return $this->processApiData($fixtures);
    }

    public function fetchEndedFixtures(): array
    {
        $fixtures = $this->model
            ->join('leagues', 'leagues.id', '=', 'fixtures.league_id')
            ->where('fixtures.status', '<>', 'NS')
            ->where('leagues.in_use', 1)
            ->whereRaw("NOW() BETWEEN fixtures.date AND DATE_ADD(fixtures.date, INTERVAL 4 HOUR)")
            ->orderBy('fixtures.date', 'desc')
            ->select(['fixtures.*'])
            ->get();

        foreach ($fixtures as $fixture) {
            $this->validatePredictions($fixture);
        }

        return $this->processApiData($fixtures);
    }

    private function processApiData(Collection|SupportCollection $fixtures): array
    {
        $apiData = [];

        foreach ($fixtures as $fixture) {
            $response = $this->fetchApiFixtures([
                'id' => $fixture->api_id,
            ]);

            $apiData = [...$apiData, ...$response];

            usleep(100);
        }

        return $apiData;
    }

    public function validatePredictions(FixtureModel $fixture): void
    {
        $fixture->load('prediction');

        if ($this->model::isFinished($fixture->status)) {
            if ($fixture->prediction) {
                Cache::tags("1X2-{$fixture->id}")->flush();

                $statuses = [];

                // 1x2
                if (!empty($fixture->prediction->{'1x2'}) && (isset($fixture->prediction->{'1x2'}['status']) && $fixture->prediction->{'1x2'}['status'] === 'available')) {
                    $winner = (int) $fixture->goals_home > (int) $fixture->goals_away ? 'H' : ((int) $fixture->goals_home < (int) $fixture->goals_away ? 'A' : null);

                    $probabilities = [
                        $fixture->prediction->{'1x2'}['home_probability'],
                        $fixture->prediction->{'1x2'}['draw_probability'],
                        $fixture->prediction->{'1x2'}['away_probability'],
                    ];
                    $max = max($probabilities);

                    $correct = (
                        empty($fixture->prediction->{'1x2'}['aggregated']) &&
                        (
                            (is_null($winner) && ($max === $fixture->prediction->{'1x2'}['draw_probability']))
                            ||
                            (!is_null($winner) && (
                                (($winner === 'H') && ($max === $fixture->prediction->{'1x2'}['home_probability']))
                                ||
                                (($winner === 'A') && ($max === $fixture->prediction->{'1x2'}['away_probability']))
                            ))
                        )
                    ) ||
                        (
                            !empty($fixture->prediction->{'1x2'}['aggregated']) &&
                            (
                                (($winner === 'H' || is_null($winner)) && (!empty($fixture->prediction->{'1x2'}['aggregated_predictions']['1x']) && !empty($fixture->prediction->{'1x2'}['aggregated_predictions']['x2']) && $fixture->prediction->{'1x2'}['aggregated_predictions']['1x'] >= $fixture->prediction->{'1x2'}['aggregated_predictions']['x2']))
                                ||
                                (($winner === 'A' || is_null($winner)) && (!empty($fixture->prediction->{'1x2'}['aggregated_predictions']['1x']) && !empty($fixture->prediction->{'1x2'}['aggregated_predictions']['x2']) && $fixture->prediction->{'1x2'}['aggregated_predictions']['1x'] <= $fixture->prediction->{'1x2'}['aggregated_predictions']['x2']))
                            )
                        );

                    $statuses['correct_1x2'] = $correct;
                }

                // corners (under) /*- A maior probabilidade aponta para a maior certeza que os cantos vão ser under*/
                if (!empty($fixture->prediction->corners) && (isset($fixture->prediction->corners['status']) && $fixture->prediction->corners['status'] === 'available')) {
                    $corners = $fixture->corners_home + $fixture->corners_away;
                    $statuses['correct_corners'] =
                        ($fixture->prediction->corners['probability'] >= self::PROBABILITIES_THRESHOLD && $corners <= $fixture->prediction->corners['market_type_value'])
                        ||
                        ($fixture->prediction->corners['probability'] < self::PROBABILITIES_THRESHOLD && $corners > $fixture->prediction->corners['market_type_value'])
                    ;
                }

                // goals (over)
                if (!empty($fixture->prediction->goals) && (isset($fixture->prediction->goals['status']) && $fixture->prediction->goals['status'] === 'available')) {
                    $goals = (int) $fixture->goals_home + (int) $fixture->goals_away;
                    $statuses['correct_goals'] =
                        ($fixture->prediction->goals['probability'] >= self::PROBABILITIES_THRESHOLD && $goals > $fixture->prediction->goals['market_type_value'])
                        ||
                        ($fixture->prediction->goals['probability'] < self::PROBABILITIES_THRESHOLD && $goals <= $fixture->prediction->goals['market_type_value']);
                }

                // cards (over)
                if (!empty($fixture->prediction->cards) && (isset($fixture->prediction->cards['status']) && $fixture->prediction->cards['status'] === 'available')) {
                    $cards = $fixture->cards_home + $fixture->cards_away;
                    $statuses['correct_cards'] =
                        ($fixture->prediction->cards['probability'] >= self::PROBABILITIES_THRESHOLD && $cards > $fixture->prediction->cards['market_type_value'])
                        ||
                        ($fixture->prediction->cards['probability'] < self::PROBABILITIES_THRESHOLD && $cards <= $fixture->prediction->cards['market_type_value']);
                }

                if (!empty($statuses)) {
                    $this->fixturePredictionModel::where('id', $fixture->prediction->id)->update($statuses);
                }
            }
        }
    }

    public function endWrongLiveFixtures(array $fixtures): void
    {
        foreach ($fixtures as $fixture) {
            $fixtureModel = $this->model->findByApiId($fixture->fixture->id)?->first();

            if ($fixtureModel) {
                if (!$fixtureModel->isFinished($fixtureModel->status)) {
                    if (!($this->between3HoursAndAHalf(strtotime($fixtureModel->date)))) {
                        $fixtureModel->update([
                            'is_live' => 0,
                            'status' => 'FT',
                            'elapsed' => 90,
                        ]);
                    }
                }
            }
        }
    }
    public function between3HoursAndAHalf(int $timestamp): bool
    {
        return $timestamp < strtotime('now') && strtotime('now') < ($timestamp + (3 * 3600) + 1800);
    }

    public function fetchJustEnded(): array
    {
        $fixturesArr = [];
        $fixtures = $this->fetchApiFixtures(['status' => '2H', 'last' => 20]);

        foreach ($fixtures as $fixture) {
            $fixtureModel = $this->model
                ->findByApiId($fixture->fixture->id)
                ->first();

            if ($fixtureModel) {
                $fixturesArr[] = $fixtureModel;
            }
        }

        return $this->processApiData(collect($fixturesArr));
    }

    public function getFixturesListFollowingCriteria(string $param, int $teamId, int $leagueId): Collection
    {
        return $this->model
            ->join('league_seasons', function ($join) {
                $join->on('league_seasons.league_id', '=', 'fixtures.league_id');
                $join->on('league_seasons.current', '=', DB::raw("1"));
            })
            ->join('leagues', 'leagues.id', '=', 'fixtures.league_id')
            ->whereRaw("fixtures.season_id = league_seasons.season_id")
            ->whereIn('status', ['FT', 'AET', 'PEN', 'ABD', 'AWD', 'WO'])
            ->whereDate('fixtures.date', '<', now())
            ->where('leagues.in_use', 1)
            ->where(function ($q) use ($teamId) {
                $q->where('team_home', $teamId)
                    ->orWhere('team_away', $teamId);
            })
            ->where('fixtures.league_id', $leagueId)
            ->where(function ($q) use ($param) {
                switch ($param) {
                    case 'goals_over_15': {
                        $q->whereRaw("(goals_home + goals_away) >= 2");

                        break;
                    }
                    case 'goals_over_25': {
                        $q->whereRaw("(goals_home + goals_away) >= 3");

                        break;
                    }
                    case 'btts': {
                        $q->whereRaw("goals_home > 0 AND goals_away > 0");

                        break;
                    }
                    case 'cards_over45': {
                        $q->whereRaw("(cards_home + cards_home) >= 5");

                        break;
                    }
                    case 'corners_under_105': {
                        $q->whereRaw("(corners_home + corners_away) <= 10");

                        break;
                    }
                    default:
                        break;
                }
            })
            ->select(['fixtures.*'])
            ->get();
    }

    public function getLastRound(int $leagueId): ?FixtureModel
    {
        return $this->model
            ->join('league_seasons', function ($join) {
                $join->on('league_seasons.league_id', '=', 'fixtures.league_id');
                $join->on('league_seasons.current', '=', DB::raw("1"));
            })
            ->join('leagues', 'leagues.id', '=', 'fixtures.league_id')
            ->where('fixtures.league_id', $leagueId)
            ->whereRaw("fixtures.season_id = league_seasons.season_id")
            ->whereIn('status', ['FT', 'AET', 'PEN', 'ABD', 'AWD', 'WO'])
            ->whereDate('fixtures.date', '<', now())
            ->where('leagues.in_use', 1)
            ->orderBy('fixtures.date', 'desc')
            ->first();
    }

    public function getEndedFixturesForSpecificRounds(int $leagueId, array $rounds, Request $request): Collection
    {
        $seasonId = $request->get('season_id');

        return $this->model
            ->join('league_seasons', function ($join) {
                $join->on('league_seasons.league_id', '=', 'fixtures.league_id');
                $join->on('league_seasons.current', '=', DB::raw("1"));
            })
            ->join('leagues', 'leagues.id', '=', 'fixtures.league_id')
            ->where('fixtures.league_id', $leagueId)
            ->where(function ($q) use ($seasonId) {
                if ($seasonId) {
                    $q->where("fixtures.season_id", $seasonId);
                } else {
                    $q->whereRaw("fixtures.season_id = league_seasons.season_id");
                }
            })
            ->whereIn('fixtures._round', $rounds)
            ->where('leagues.in_use', 1)
            ->orderBy('fixtures.date', 'asc')
            ->groupBy('fixtures.id')
            ->select(['fixtures.*'])
            ->get();
    }

    public function getMatchOfTheDay(Request $request, string $date, string $tzOffset): ?object
    {
        $leaguesInUseIds = Cache::rememberForever(
            'leagues-in-use-ids',
            fn() => $this->leagueModel->getInUseLeagues()->pluck('id')->toArray()
        );

        $todayFixtures = $this->model
            ->fixturesLimitsAndOffsetsForADay($request, $date, $tzOffset, $leaguesInUseIds);

        $todayFixturesIds = $todayFixtures->pluck('id')->toArray();

        $fixture = $this->model
            ->getNextMatchOrderByCountryAndLeague($request, $todayFixturesIds, $date, $tzOffset);

        return $fixture;
    }
}
