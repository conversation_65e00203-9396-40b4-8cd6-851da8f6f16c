<?php

namespace App\Services\JogosHoje;

use App\Http\Clients\JogosHoje\{BetanoHttpClient, Bet365HttpClient};
use Illuminate\Support\Arr;

class BookmakerOddsService
{
    public function __construct(
        private readonly BetanoHttpClient $betano,
        private readonly Bet365HttpClient $bet365,
    ) {}

    /**
     * Fetch odds from all integrated bookmakers for a given event.
     *
     * @param int $eventId Identifier of the event in bookmaker feeds
     * @param string $country ISO country code (e.g. BR)
     */
    public function getOdds(int $eventId, string $country = 'BR'): array
    {
        $standardizedOdds = [];

        // Get Betano odds
        $betanoOdds = $this->getBetanoOdds($eventId);
        if (!empty($betanoOdds)) {
            $standardizedOdds = array_merge($standardizedOdds, $betanoOdds);
        }

        // Get Bet365 odds
        $bet365Odds = $this->getBet365Odds($eventId);
        if (!empty($bet365Odds)) {
            $standardizedOdds = array_merge($standardizedOdds, $bet365Odds);
        }

        return $standardizedOdds;
    }

    /**
     * Get Betano odds for event
     */
    private function getBetanoOdds(int $eventId): array
    {
        try {
            $response = $this->betano->getCombinedOdds(['eventid' => $eventId]);
            return $this->convertBetanoToStandardFormat($response);
        } catch (\Throwable $e) {
            logger('Betano odds error: '.$e->getMessage());
            return [];
        }
    }

    /**
     * Get Bet365 odds for event
     */
    private function getBet365Odds(int $eventId): array
    {
        try {
            $params = [
                'EventGroupID' => $eventId,
                'MarketID' => 75, // Match Winner
                'LanguageID' => 1,
            ];
            $response = $this->bet365->getSoccerOdds($params);
            return $this->convertBet365ToStandardFormat($response);
        } catch (\Throwable $e) {
            logger('Bet365 odds error: '.$e->getMessage());
            return [];
        }
    }

    /**
     * Convert Betano response to standard format
     */
    private function convertBetanoToStandardFormat(array $response): array
    {
        $standardizedOdds = [];

        if (!isset($response['events']) || !is_array($response['events'])) {
            return $standardizedOdds;
        }

        foreach ($response['events'] as $event) {
            if (!isset($event['markets']) || !is_array($event['markets'])) {
                continue;
            }

            $eventOdds = (object) [
                'bookmakers' => [(object) [
                    'id' => 1001, // Betano ID
                    'name' => 'Betano',
                    'bets' => []
                ]]
            ];

            foreach ($event['markets'] as $market) {
                $marketId = $this->mapBetanoMarketId($market['name'] ?? '');
                if (!$marketId) continue;

                $bet = (object) [
                    'id' => $marketId,
                    'name' => $market['name'] ?? '',
                    'values' => []
                ];

                if (isset($market['selections']) && is_array($market['selections'])) {
                    foreach ($market['selections'] as $selection) {
                        $bet->values[] = (object) [
                            'value' => $selection['name'] ?? '',
                            'odd' => (string) ($selection['odds'] ?? '0')
                        ];
                    }
                }

                $eventOdds->bookmakers[0]->bets[] = $bet;
            }

            if (!empty($eventOdds->bookmakers[0]->bets)) {
                $standardizedOdds[] = $eventOdds;
            }
        }

        return $standardizedOdds;
    }

    /**
     * Convert Bet365 response to standard format
     */
    private function convertBet365ToStandardFormat(array $response): array
    {
        $standardizedOdds = [];

        if (!isset($response['Event'])) {
            return $standardizedOdds;
        }

        $events = isset($response['Event'][0]) ? $response['Event'] : [$response['Event']];

        foreach ($events as $event) {
            $eventOdds = (object) [
                'bookmakers' => [(object) [
                    'id' => 1002, // Bet365 ID
                    'name' => 'Bet365',
                    'bets' => []
                ]]
            ];

            if (isset($event['Market'])) {
                $markets = isset($event['Market'][0]) ? $event['Market'] : [$event['Market']];

                foreach ($markets as $market) {
                    $marketId = $this->mapBet365MarketId($market['@Name'] ?? '');
                    if (!$marketId) continue;

                    $bet = (object) [
                        'id' => $marketId,
                        'name' => $market['@Name'] ?? '',
                        'values' => []
                    ];

                    if (isset($market['Participant'])) {
                        $participants = isset($market['Participant'][0]) ? $market['Participant'] : [$market['Participant']];

                        foreach ($participants as $participant) {
                            $bet->values[] = (object) [
                                'value' => $participant['@Name'] ?? '',
                                'odd' => (string) ($participant['@Odds'] ?? '0')
                            ];
                        }
                    }

                    $eventOdds->bookmakers[0]->bets[] = $bet;
                }
            }

            if (!empty($eventOdds->bookmakers[0]->bets)) {
                $standardizedOdds[] = $eventOdds;
            }
        }

        return $standardizedOdds;
    }

    /**
     * Map Betano market names to internal market IDs
     */
    private function mapBetanoMarketId(string $marketName): ?int
    {
        $mappings = [
            '1X2' => 1,
            'Match Winner' => 1,
            'Double Chance' => 12,
            'Goals Over/Under' => 5,
            'Total Goals' => 5,
            'Both Teams to Score' => 8,
            'BTTS' => 8,
            'Corners Over/Under' => 45,
            'Total Corners' => 45,
            'Cards Over/Under' => 80,
            'Total Cards' => 80,
        ];

        foreach ($mappings as $pattern => $marketId) {
            if (stripos($marketName, $pattern) !== false) {
                return $marketId;
            }
        }

        return null;
    }

    /**
     * Map Bet365 market names to internal market IDs
     */
    private function mapBet365MarketId(string $marketName): ?int
    {
        $mappings = [
            'Match Result' => 1,
            '1X2' => 1,
            'Double Chance' => 12,
            'Goals Over/Under' => 5,
            'Total Goals' => 5,
            'Both Teams to Score' => 8,
            'BTTS' => 8,
            'Corners' => 45,
            'Cards' => 80,
        ];

        foreach ($mappings as $pattern => $marketId) {
            if (stripos($marketName, $pattern) !== false) {
                return $marketId;
            }
        }

        return null;
    }

    /**
     * Get standardized odds format for value bet calculations
     */
    public function getStandardizedOddsForFixture($fixture): array
    {
        // For now, return empty array to avoid breaking existing functionality
        // This will be populated with real bookmaker odds
        return [];
    }
}