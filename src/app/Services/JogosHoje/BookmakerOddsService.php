<?php

namespace App\Services\JogosHoje;

use App\Http\Clients\Bookmakers\BetanoHttpClient;
use App\Http\Clients\Bookmakers\Bet365HttpClient;
use Illuminate\Support\Arr;

class BookmakerOddsService
{
    public function __construct(
        private readonly BetanoHttpClient $betano,
        private readonly Bet365HttpClient $bet365,
    ) {}

    /**
     * Fetch odds from all integrated bookmakers for a given event.
     *
     * @param int $eventId Identifier of the event in bookmaker feeds
     * @param string $country ISO country code (e.g. BR)
     */
    public function getOdds(int $eventId, string $country = 'BR'): array
    {
        $odds = [];

        // Betano odds
        try {
            $odds['betano'] = $this->betano->getEventOdds($eventId);
        } catch (\Throwable $e) {
            logger('Betano odds error: '.$e->getMessage());
        }

        // Bet365 odds
        try {
            $params = [
                'EventGroupID' => $eventId,
                'MarketID' => 75,
                'LanguageID' => 1,
            ];
            $odds['bet365'] = $this->bet365->getOdds($params);
        } catch (\Throwable $e) {
            logger('Bet365 odds error: '.$e->getMessage());
        }

        return $odds;
    }
}