<?php

namespace App\Services\JogosHoje;

use App\Contracts\JogosHoje\Service\ServiceInterface;
use App\Models\JogosHoje\Fixture;
use App\Models\JogosHoje\League;
use App\Models\JogosHoje\Team;
use App\Traits\ScoutIndexPrefix;
use Meilisearch\Client;

readonly class MeiliSearchService implements ServiceInterface
{
    use ScoutIndexPrefix;
    public function __construct(
        private Client $meiliSearch,
        private Fixture $fixtureModel,
        private League $leagueModel,
        private Team $teamModel,
    ) {
    }

    public function handle($type): void
    {
        if ($type === SearchService::SEARCH_LEAGUES_TYPE) {
            $leaguesIndexName = $this->getIndexPrefixByEnvironment() . SearchService::SEARCH_LEAGUES_TYPE;
            $this->meiliSearch->deleteIndex($leaguesIndexName);
            $this->meiliSearch->index($leaguesIndexName)->updateSortableAttributes(['id', 'popularity']);
            $this->leagueModel::makeAllSearchable();
        }

        if ($type === SearchService::SEARCH_FIXTURES_TYPE) {
            $fixturesIndexName = $this->getIndexPrefixByEnvironment() . SearchService::SEARCH_FIXTURES_TYPE;
            $this->meiliSearch->deleteIndex($fixturesIndexName);
            $this->fixtureModel::makeAllSearchable();
            $this->meiliSearch->index($fixturesIndexName)->updateSortableAttributes(['date']);
        }

        if ($type === SearchService::SEARCH_TEAMS_TYPE) {
            $teamsIndexName = $this->getIndexPrefixByEnvironment() . SearchService::SEARCH_TEAMS_TYPE;
            $this->meiliSearch->deleteIndex($teamsIndexName);
            $this->teamModel::makeAllSearchable();
            $this->meiliSearch->index($teamsIndexName)->updateSortableAttributes(['id', 'popularity']);
        }
    }
}
