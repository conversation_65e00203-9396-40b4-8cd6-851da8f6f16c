<?php

namespace App\Services\JogosHoje;

use App\Http\Resources\JogosHoje\SearchResource;
use App\Models\JogosHoje\Fixture;
use App\Models\JogosHoje\League;
use App\Models\JogosHoje\Team;
use \Illuminate\Database\Eloquent\Collection;

class SearchService
{
    public const string SEARCH_FIXTURES_TYPE = 'fixtures';
    public const string SEARCH_LEAGUES_TYPE = 'leagues';
    public const string SEARCH_TEAMS_TYPE = 'teams';
    public const string SEARCH_ALL_TYPE = 'all';
    public function __construct(
        private readonly Fixture $fixtureModel,
        private readonly League $leagueModel,
        private readonly Team $teamModel,
        private readonly GoogleTranslateService $translateService
    ) {
    }

    public function search(string $term, string $type = 'all'): array
    {
        $options = [
            'sort' => ['popularity:desc'],
        ];

        $translatedTerm = $this->translateService->translateText($term);

        return match ($type) {
            self::SEARCH_FIXTURES_TYPE => SearchResource::collection($this->handleFixtureSearch($term))->toArray(request()),
            self::SEARCH_LEAGUES_TYPE => SearchResource::collection($this->leagueModel::search($translatedTerm)->options($options)->get()->load(['country']))->toArray(request()),
            self::SEARCH_TEAMS_TYPE => SearchResource::collection($this->handleTeamSearch($term))->toArray(request()),
            self::SEARCH_ALL_TYPE => $this->multiSearch($term, $options),
            default => ['Invalid search type'],
        };
    }

    private function handleTeamSearch(string $term): Collection
    {
        $options = [
            'sort' => ['popularity:desc'],
        ];
        return $this->teamModel::search($term)->options($options)->get()->load(['leagues.country', 'country']);
    }

    private function multiSearch(string $term, ?array $options = null): array
    {
        $translatedTerm = $this->translateService->translateText($term);

        return [
            'fixtures' => SearchResource::collection($this->handleFixtureSearch($term))->toArray(request()),
            'teams' => SearchResource::collection($this->teamModel::search($term)->options($options)->get()->load(['leagues.country', 'country']))->toArray(request()),
            'leagues' => SearchResource::collection($this->leagueModel::search($translatedTerm)->get()->load(['country']))->toArray(request()),
        ];
    }

    private function handleFixtureSearch(string $term): Collection
    {
        $fixtures = new Collection();

        $teams = $this->handleTeamSearch($term);

        $teams->each(function ($team) use (&$fixtures) {
            $matches = $this->fixtureModel
                ->with(['homeTeam', 'awayTeam', 'league.country'])
                ->join('leagues', 'leagues.id', '=', 'fixtures.league_id')
                ->where('leagues.in_use', 1)
                ->where(function ($query) use ($team) {
                    $query->where('fixtures.team_home', $team->id)
                        ->orWhere('fixtures.team_away', $team->id);
                })
                ->whereIn("fixtures.status", ['FT', 'NS', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO'])
                ->whereRaw("(fixtures.date >= DATE_SUB(NOW(), INTERVAL 7 DAY) AND (fixtures.date <= DATE_ADD(NOW(), INTERVAL 14 DAY)))")
                ->orderBy('fixtures.date', 'asc')
                ->select('fixtures.*')
                ->get();

            foreach ($matches as $match) {
                if (!$fixtures->contains('id', $match->id)) {
                    $fixtures->push($match);
                }
            }
        });

        return $fixtures;
    }
}
