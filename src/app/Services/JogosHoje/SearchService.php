<?php

namespace App\Services\JogosHoje;

use App\Http\Resources\JogosHoje\LeagueResourceSmall;
use App\Http\Resources\JogosHoje\TeamResourceSmall;
use App\Http\Resources\LogoResource;
use App\Models\JogosHoje\League;
use App\Models\JogosHoje\Team;
use App\Models\MediaSite;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class SearchService
{
    private const string LEAGUES_TYPE = 'leagues';
    private const string TEAMS_TYPE = 'teams';
    public function __construct(
        private readonly MediaSite $mediaSiteModel,
        private readonly League $leagueModel,
        private readonly Team $teamModel
    ) {
    }

    private function searchForLeagues(string $term): AnonymousResourceCollection
    {
        return LeagueResourceSmall::collection($this->leagueModel->where('name', 'like', "%$term%")->limit(25)->get());
    }

    private function searchForTeams(string $term): AnonymousResourceCollection
    {
        return TeamResourceSmall::collection($this->teamModel->where('name', 'like', "%$term%")->limit(25)->get());
    }

    private function getSearchQueries(string $term): array
    {
        return [
            'countries' => <<<COUNTRIES_QUERY
                SELECT * FROM (
                    SELECT
                        c.id,
                        NULL elapsed,
                        0 is_live,
                        NULL is_cup,
                        c.slug country_slug,
                        c.name country_name,
                        l.slug league_slug,
                        l.name league_name,
                        NULL team_slug,
                        NULL team_name,
                        c.media_site_id,
                        c.media_site_id country_media_site_id,
                        NULL league_media_site_id,
                        NULL date,
                        'countries' type,
                        'Países' title,
                        NULL goals_home,
                        NULL goals_away
                    FROM countries c
                    LEFT JOIN leagues l ON l.id = c.primary_league_id
                    WHERE
                        LOWER(c.name) LIKE '%{$term}%' AND
                        c.primary_league_id IS NOT NULL
                    ORDER BY c._order ASC
                    LIMIT 6
                ) a
            COUNTRIES_QUERY,
            'leagues' => <<<LEAGUES_QUERY
SELECT * FROM (
                SELECT
                    l.id,
                    NULL elapsed,
                    0 is_live,
                    l.is_cup,
                    c.slug country_slug,
                    c.name country_name,
                    l.slug league_slug,
                    l.name league_name,
                    NULL team_slug,
                    NULL team_name,
                    l.media_site_id,
                    c.media_site_id country_media_site_id,
                    l.media_site_id league_media_site_id,
                    NULL date,
                    'leagues' type,
                    'Campeonatos' title,
                    NULL goals_home,
                    NULL goals_away
                FROM leagues l
                JOIN countries c ON c.id = l.country_id
                WHERE
                    l.in_use = 1 AND
                    LOWER(l.name) LIKE '%{$term}%'
                ORDER BY l._order ASC
                LIMIT 6
            ) b
LEAGUES_QUERY,
            'teams' => <<<TEAMS_QUERY
SELECT * FROM (
                SELECT
                    t.id,
                    NULL elapsed,
                    0 is_live,
                    NULL is_cup,
                    c.slug country_slug,
                    c.name country_name,
                    NULL league_slug,
                    NULL league_name,
                    t.slug team_slug,
                    t.name team_name,
                    t.media_site_id,
                    c.media_site_id country_media_site_id,
                    NULL league_media_site_id,
                    NULL date,
                    'teams' type,
                    'Times' title,
                    NULL goals_home,
                    NULL goals_away
                FROM teams t
                JOIN countries c ON c.id = t.country_id
                WHERE
                    LOWER(t.name) LIKE '%{$term}%'
                ORDER BY t._order ASC
                LIMIT 6
            ) c
TEAMS_QUERY,
            'fixtures' => <<<FIXTURES_QUERY
SELECT * FROM (
                SELECT
                    f.id,
                    f.elapsed,
                    f.is_live,
                    NULL is_cup,
                    c.slug country_slug,
                    c.name country_name,
                    l.slug league_slug,
                    l.name league_name,
                    CONCAT(t1.slug,'|',t2.slug) team_slug,
                    CONCAT(t1.name,'|',t2.name) team_name,
                    CONCAT(t1.media_site_id,'|',t2.media_site_id) media_site_id,
                    c.media_site_id country_media_site_id,
                    l.media_site_id league_media_site_id,
                    f.date date,
                    'fixtures' type,
                    'Jogos' title,
                    IF(goals_home IS NOT NULL, (COALESCE(goals_home, 0) + COALESCE(goals_extra_home, 0)), NULL) goals_home,
                    IF(goals_away IS NOT NULL, (COALESCE(goals_away, 0) + COALESCE(goals_extra_away, 0)), NULL) goals_away
                FROM fixtures f
                JOIN leagues l ON l.id = f.league_id
                JOIN countries c ON c.id = l.country_id
                JOIN teams t1 ON t1.id = f.team_home
                JOIN teams t2 ON t2.id = f.team_away
                WHERE
                    l.in_use = 1 AND
                    f.status IN ('FT', 'NS', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND
                    (LOWER(t1.name) LIKE '%{$term}%' OR LOWER(t2.name) LIKE '%{$term}%') AND
                    (f.date >= DATE_SUB(NOW(), INTERVAL 4 DAY) AND (f.date <= DATE_ADD(NOW(), INTERVAL 4 DAY)))
                ORDER BY f.date ASC
                LIMIT 6
            ) d
FIXTURES_QUERY
        ];
    }

    public function search(string $term, string $type = null): Collection
    {
        $term = strtolower($term);

        $queries = $this->getSearchQueries($term);

        if ($type === self::LEAGUES_TYPE) {
            return collect($this->searchForLeagues($term));
        }

        if ($type === self::TEAMS_TYPE) {
            return collect($this->searchForTeams($term));
        }

        $query = "
            {$queries['countries']}
            UNION ALL
            {$queries['leagues']}
            UNION ALL
            {$queries['teams']}
            UNION ALL
             {$queries['fixtures']}
        ";

        return collect(DB::select($query))->map(function ($item) {
            if ($item->type !== 'fixtures') {
                $item->media = $item->media_site_id ? new LogoResource($this->mediaSiteModel::find($item->media_site_id)) : null;
                $item->country_media = $item->country_media_site_id ? new LogoResource($this->mediaSiteModel::find($item->country_media_site_id)) : null;
                return $item;
            } else {
                if ($item->media_site_id) {
                    [$home_team_media_id, $away_team_media_id] = explode('|', $item->media_site_id);
                }

                [$home_team_name, $away_team_name] = explode('|', $item->team_name);
                [$home_team_slug, $away_team_slug] = explode('|', $item->team_slug);

                $item->home_team = [
                    'name' => $home_team_name,
                    'slug' => $home_team_slug,
                    'media' => $item->media_site_id ? new LogoResource($this->mediaSiteModel::find($home_team_media_id)) : null,
                ];
                $item->away_team = [
                    'name' => $away_team_name,
                    'slug' => $away_team_slug,
                    'media' => $item->media_site_id ? new LogoResource($this->mediaSiteModel::find($away_team_media_id)) : null,
                ];
                $item->is_live = $item->is_live === 1;
                $item->media = $item->league_media_site_id ? new LogoResource($this->mediaSiteModel::find($item->league_media_site_id)) : null;
                $item->country_media = $item->country_media_site_id ? new LogoResource($this->mediaSiteModel::find($item->country_media_site_id)) : null;

                return $item;
            }
        })->groupBy('type');
    }
}
