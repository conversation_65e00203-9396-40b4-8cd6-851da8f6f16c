<?php

namespace App\Services\JogosHoje;

use Google\ApiCore\ApiException;
use Google\Cloud\Translate\V3\Client\TranslationServiceClient;
use Google\Cloud\Translate\V3\TranslateTextRequest;
use Illuminate\Support\Facades\Log;

class GoogleTranslateService
{
    public function translateText(string $text): string
    {
        $translationServiceClient = new TranslationServiceClient([
            'credentials' => json_decode(config('services.google.credentials_json'), true)
        ]);

        try {
            $projectId = config('services.google.project_id');
            $parent = $translationServiceClient::locationName(
                $projectId,
                'global'
            );

            $request = new TranslateTextRequest();
            $request->setParent($parent);
            $request->setMimeType('text/plain');
            $request->setTargetLanguageCode('en');
            $request->setContents([$text]);

            $response = $translationServiceClient->translateText($request);

            $translations = $response->getTranslations();
            return $translations[0]->getTranslatedText();
        } catch (ApiException $ex) {
            Log::error('Google Translate API error', [
                'message' => $ex->getMessage(),
                'code' => $ex->getCode(),
                'text' => $text,
            ]);
            return $text;
        } finally {
            $translationServiceClient->close();
        }
    }
}
