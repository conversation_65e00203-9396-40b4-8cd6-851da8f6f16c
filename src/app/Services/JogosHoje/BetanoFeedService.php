<?php

namespace App\Services\JogosHoje;

use App\Http\Clients\Bookmakers\BetanoOddsClient;
use Illuminate\Support\Facades\Log;

class BetanoFeedService
{
    public function __construct(
        private readonly BetanoOddsClient $client
    ) {}

    /**
     * Get standardized odds for all football events
     */
    public function getAllFootballOdds(): array
    {
        $rawData = $this->client->getFootballOdds();
        return $this->standardizeOddsData($rawData);
    }

    /**
     * Get standardized odds for a specific event
     */
    public function getEventOdds(string $eventId): array
    {
        $rawData = $this->client->getEventOdds($eventId);
        return $this->standardizeEventOdds($rawData);
    }

    /**
     * Get odds for a specific league
     */
    public function getLeagueOdds(string $leagueId): array
    {
        $rawData = $this->client->getLeagueOdds($leagueId);
        return $this->standardizeOddsData($rawData);
    }

    /**
     * Get today's odds only
     */
    public function getTodayOdds(): array
    {
        $rawData = $this->client->getTodayOdds();
        return $this->standardizeOddsData($rawData);
    }

    /**
     * Standardize odds data to common format
     */
    private function standardizeOddsData(array $rawData): array
    {
        if (empty($rawData['events'])) {
            return [];
        }

        $standardizedEvents = [];

        foreach ($rawData['events'] as $event) {
            $standardizedEvent = $this->standardizeEvent($event);
            if ($standardizedEvent) {
                $standardizedEvents[] = $standardizedEvent;
            }
        }

        Log::info('[BETANO_FEED_SERVICE] Standardized odds data', [
            'raw_events' => count($rawData['events']),
            'standardized_events' => count($standardizedEvents)
        ]);

        return $standardizedEvents;
    }

    /**
     * Standardize single event odds
     */
    private function standardizeEventOdds(array $rawData): array
    {
        if (empty($rawData)) {
            return [];
        }

        // For single event, the structure might be different
        if (isset($rawData['event'])) {
            return $this->standardizeEvent($rawData['event']);
        }

        // If it's in events array format
        if (isset($rawData['events']) && !empty($rawData['events'])) {
            return $this->standardizeEvent($rawData['events'][0]);
        }

        return [];
    }

    /**
     * Standardize a single event to common format
     */
    private function standardizeEvent(array $event): ?array
    {
        try {
            // Extract basic event information
            $standardized = [
                'bookmaker_id' => 1001, // Betano ID
                'bookmaker_name' => 'Betano',
                'event_id' => $event['eventId'] ?? null,
                'home_team' => $event['homeTeam'] ?? null,
                'away_team' => $event['awayTeam'] ?? null,
                'league' => $event['league'] ?? null,
                'start_time' => $event['startTime'] ?? null,
                'markets' => []
            ];

            // Extract odds/markets
            if (isset($event['odds'])) {
                $standardized['markets'] = $this->extractMarkets($event['odds']);
            }

            // Validate required fields
            if (empty($standardized['event_id']) || empty($standardized['home_team']) || empty($standardized['away_team'])) {
                Log::warning('[BETANO_FEED_SERVICE] Skipping event with missing required fields', $event);
                return null;
            }

            return $standardized;

        } catch (\Exception $e) {
            Log::error('[BETANO_FEED_SERVICE] Error standardizing event', [
                'event' => $event,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Extract and standardize markets from odds data
     */
    private function extractMarkets(array $odds): array
    {
        $markets = [];

        // 1X2 Market (Match Winner)
        if (isset($odds['1']) && isset($odds['X']) && isset($odds['2'])) {
            $markets[] = [
                'market_id' => 1,
                'market_name' => '1X2',
                'outcomes' => [
                    [
                        'outcome_id' => '1',
                        'outcome_name' => 'Home',
                        'odd' => (float) $odds['1']
                    ],
                    [
                        'outcome_id' => 'X',
                        'outcome_name' => 'Draw',
                        'odd' => (float) $odds['X']
                    ],
                    [
                        'outcome_id' => '2',
                        'outcome_name' => 'Away',
                        'odd' => (float) $odds['2']
                    ]
                ]
            ];
        }

        // Over/Under 2.5 Goals
        if (isset($odds['O2.5']) && isset($odds['U2.5'])) {
            $markets[] = [
                'market_id' => 5,
                'market_name' => 'Goals Over/Under 2.5',
                'outcomes' => [
                    [
                        'outcome_id' => 'O2.5',
                        'outcome_name' => 'Over 2.5',
                        'odd' => (float) $odds['O2.5']
                    ],
                    [
                        'outcome_id' => 'U2.5',
                        'outcome_name' => 'Under 2.5',
                        'odd' => (float) $odds['U2.5']
                    ]
                ]
            ];
        }

        // Both Teams to Score
        if (isset($odds['GG']) && isset($odds['NG'])) {
            $markets[] = [
                'market_id' => 8,
                'market_name' => 'Both Teams to Score',
                'outcomes' => [
                    [
                        'outcome_id' => 'GG',
                        'outcome_name' => 'Yes',
                        'odd' => (float) $odds['GG']
                    ],
                    [
                        'outcome_id' => 'NG',
                        'outcome_name' => 'No',
                        'odd' => (float) $odds['NG']
                    ]
                ]
            ];
        }

        // Double Chance
        if (isset($odds['1X']) && isset($odds['X2'])) {
            $markets[] = [
                'market_id' => 12,
                'market_name' => 'Double Chance',
                'outcomes' => [
                    [
                        'outcome_id' => '1X',
                        'outcome_name' => 'Home/Draw',
                        'odd' => (float) $odds['1X']
                    ],
                    [
                        'outcome_id' => 'X2',
                        'outcome_name' => 'Draw/Away',
                        'odd' => (float) $odds['X2']
                    ]
                ]
            ];
        }

        return $markets;
    }

    /**
     * Get odds in format compatible with existing value bet system
     */
    public function getOddsForValueBets(): array
    {
        $events = $this->getAllFootballOdds();
        $formattedOdds = [];

        foreach ($events as $event) {
            $formattedOdds[] = [
                'bookmakers' => [
                    [
                        'id' => $event['bookmaker_id'],
                        'name' => $event['bookmaker_name'],
                        'bets' => $this->formatMarketsForValueBets($event['markets'])
                    ]
                ]
            ];
        }

        return $formattedOdds;
    }

    /**
     * Format markets for value bet calculations
     */
    private function formatMarketsForValueBets(array $markets): array
    {
        $formattedMarkets = [];

        foreach ($markets as $market) {
            $values = [];
            foreach ($market['outcomes'] as $outcome) {
                $values[] = [
                    'value' => $outcome['outcome_name'],
                    'odd' => $outcome['odd']
                ];
            }

            $formattedMarkets[] = [
                'id' => $market['market_id'],
                'name' => $market['market_name'],
                'values' => $values
            ];
        }

        return $formattedMarkets;
    }
}
