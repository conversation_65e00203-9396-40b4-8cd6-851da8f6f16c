<?php

namespace App\Services\JogosHoje;

use App\Contracts\JogosHoje\Service\DataHandlerServiceInterface;
use App\Http\Clients\JogosHoje\PredictionsHttpClient;
use App\Models\JogosHoje\{Fixture, FixturePrediction, League, Team};
readonly class FixturePredictionService implements DataHandlerServiceInterface
{
    public function __construct(
        private FixturePrediction $model,
        private Fixture $fixtureModel,
        private Team $teamModel,
        private League $leagueModel,
        private StandingService $standingService,
        private PredictionsHttpClient $httpClient
    ) {
    }

    public function handle(array|object $data): void
    {
        if (!empty(get_object_vars($data->latest_predictions))) {
            $fixture = $this->fixtureModel->whereApiId($data->api_football_id)->first();
            if ($fixture) {
                $this->model->updateOrCreate(
                    ['fixture_id' => $fixture->id],
                    [
                        '1x2' => $data->latest_predictions->{'1x2'} ?? null,
                        'corners' => $data->latest_predictions->Corners ?? null,
                        'goals' => $data->latest_predictions->Goals ?? null,
                        'cards' => $data->latest_predictions->Cards ?? null,
                    ]
                );

                $fixture->update(['has_predictions' => true]);
            }
        }
    }
}
