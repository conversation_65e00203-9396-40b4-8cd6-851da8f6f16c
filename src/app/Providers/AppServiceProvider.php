<?php

namespace App\Providers;

use App\Http\Clients\JogosHoje\{BroadcastersHttpClient, FootballHttpClient, PredictionsHttpClient, TheSportsAPIHttpClient};
use App\Services\JogosHoje\BookmakerOddsService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register(): void
    {
        $this->app->bind(FootballHttpClient::class, function () {
            return new FootballHttpClient(
                config('services.rapidapi.host'),
                config('services.rapidapi.key')
            );
        });

        $this->app->singleton(BroadcastersHttpClient::class, function () {
            return new BroadcastersHttpClient(
                config('services.broadcasters.host'),
                config('services.broadcasters.key')
            );
        });

        $this->app->singleton(PredictionsHttpClient::class, function () {
            return new PredictionsHttpClient(
                config('services.predictions.host'),
                config('services.predictions.key')
            );
        });

        $this->app->singleton(TheSportsAPIHttpClient::class, function () {
            return new TheSportsAPIHttpClient(
                host: config('services.thesports.host'),
                skipErrors: true
            );
        });

        $this->app->singleton(\App\Http\Clients\JogosHoje\BetanoHttpClient::class, function () {
            return new \App\Http\Clients\JogosHoje\BetanoHttpClient();
        });

        $this->app->singleton(\App\Services\JogosHoje\BetanoFeedService::class, function ($app) {
            return new \App\Services\JogosHoje\BetanoFeedService(
                $app->make(\App\Http\Clients\JogosHoje\BetanoHttpClient::class)
            );
        });

        $this->app->singleton(\App\Http\Clients\JogosHoje\Bet365HttpClient::class, function () {
            return new \App\Http\Clients\JogosHoje\Bet365HttpClient();
        });

        $this->app->singleton(BookmakerOddsService::class, function ($app) {
            return new BookmakerOddsService(
                $app->make(\App\Http\Clients\JogosHoje\BetanoHttpClient::class),
                $app->make(\App\Http\Clients\JogosHoje\Bet365HttpClient::class),
                $app->make(\App\Services\JogosHoje\BetanoFeedService::class)
            );
        });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     * @throws BindingResolutionException
     */
    public function boot()
    {
        Http::macro('botmaker', function () {
            return Http::withHeaders([
                'Content-Type' => 'application/json',
                'access-token' => config('cms.jogoshoje.botmaker_access_token')
            ])->baseUrl('https://api.botmaker.com/v2.0');
        });

        JsonResource::withoutWrapping();
    }
}
