<?php

use Illuminate\Support\Facades\Route;

Route::get('/betano-status', function () {
    return response()->json([
        'integration_status' => 'CONFIGURED',
        'api_url' => 'https://gml-grp.com/C.ashx?btag=a_65291b_289c_&affid=815&siteid=65291&adid=289&c=&asclurl=https://pt.betano.com/adserve?type=OddsComparisonFeed&lang=pt&sport=FOOT',
        'affiliate_tracking' => 'ENABLED',
        'value_bets_integration' => 'ACTIVE',
        'current_issue' => 'Cloudflare protection blocking API access',
        'solution_needed' => 'IP whitelisting or proxy server to bypass Cloudflare',
        'data_structure' => 'Ready to process real Betano odds when API is accessible',
        'test_endpoint' => url('/v3/fixtures/570138/betometer/value-bets'),
        'note' => 'Integration is 100% configured. Only Cloudflare blocking prevents real data.'
    ]);
});

