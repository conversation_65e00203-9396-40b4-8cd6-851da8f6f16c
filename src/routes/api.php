<?php

use Illuminate\Support\Facades\Route;

Route::get('/test', function () {
    return response()->json(['message' => 'API is working']);
});

// Test Betano affiliate connection
Route::get('/test/betano-affiliate', [App\Http\Controllers\TestBetanoAffiliateController::class, 'testAffiliateConnection']);
Route::get('/test/betano-multiple-events', [App\Http\Controllers\TestBetanoAffiliateController::class, 'testMultipleEvents']);
Route::get('/test/betano-exact-url', [App\Http\Controllers\TestBetanoAffiliateController::class, 'testExactUrl']);
Route::get('/test/betano-analyze', [App\Http\Controllers\TestBetanoAffiliateController::class, 'analyzeResponse']);
Route::get('/test/betano-simulated', [App\Http\Controllers\TestBetanoAffiliateController::class, 'testWithSimulatedData']);
Route::get('/test/betano-final-test', [App\Http\Controllers\TestBetanoAffiliateController::class, 'finalIntegrationTest']);

