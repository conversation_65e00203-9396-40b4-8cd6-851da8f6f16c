APP_NAME="Jogos Hoje API"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

# Betano API Configuration
BETANO_DOMAIN=pt.betano.com
BETANO_LANGUAGE=pt
BETANO_DAYS_AHEAD=7
BETANO_MAX_EVENTS=100

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=jogoshoje
DB_USERNAME=jogoshoje
DB_PASSWORD=jogoshoje

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_CLIENT=predis

MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

API_HOST="v3.football.api-sports.io"
API_KEY=""

BROADCASTS_HOST="wosti-futebol-tv-brasil.p.rapidapi.com"
BROADCASTS_KEY=""

PREDICTIONS_API_HOST="https://api.jogoshoje.cron.studio"
PREDICTIONS_API_KEY=""

DO_SPACES_KEY=""
DO_SPACES_SECRET=""
DO_SPACES_REGION=""
DO_SPACES_BUCKET=""
DO_SPACES_URL=""
DO_SPACES_ENDPOINT=""

PREFIX="/dashboard"
JOGOS_HOJE_DOMAIN="jogoshoje.com"
JOGOS_HOJE_API_DOMAIN="api.jogoshoje.com"
JH_SITE_ID=5

BOTMAKER_USER_ID=
BOTMAKER_NON_IDENTIFIED_ID=
BOTMAKER_ACCESS_TOKEN=
BOTMAKER_UPDATE_TOKEN=

SAIL_XDEBUG_MODE=develop,debug
SAIL_XDEBUG_CONFIG="client_host=host.docker.internal"

RECAPTCHA_SECRET=
