{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.3", "ext-gd": "*", "ext-redis": "*", "aws/aws-sdk-php": "^3.263", "buglinjo/laravel-webp": "^2.3", "doctrine/dbal": "^3.5", "dyrynda/laravel-cascade-soft-deletes": "^4.4", "fakerphp/faker": "^1.23", "guzzlehttp/guzzle": "^7.2", "hashids/hashids": "^4.1.0", "inertiajs/inertia-laravel": "^0.6.3", "intervention/image": "^2.7", "karmendra/laravel-agent-detector": "^1.1", "laravel/framework": "^10.0", "laravel/sanctum": "^3.2", "laravel/socialite": "^5.8", "laravel/tinker": "^2.7", "laravel/vapor-core": "^2.37", "league/flysystem-aws-s3-v3": "^3.13", "predis/predis": "^2.2", "prodigyphp/laravel-ssh-tunnel": "^3.4", "spatie/laravel-sitemap": "^7.2", "symfony/http-client": "^7.1", "symfony/mailgun-mailer": "^7.1", "tightenco/ziggy": "^1.0", "torann/geoip": "^3.0", "watson/validating": "^8.0"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.8", "friendsofphp/php-cs-fixer": "^3.72", "laravel/breeze": "^1.18", "laravel/pint": "^1.0", "laravel/sail": "^1.25", "laravel/vapor-cli": "^1.64", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"files": ["app/Helpers/currentSite.php", "app/Helpers/translate.php"], "classmap": ["app/Http/Clients/Bookmakers"], "psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "prefer-stable": true}