<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('odds', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('fixture_id');
            $table->unsignedBigInteger('home_team_id');
            $table->unsignedBigInteger('away_team_id');
            $table->unsignedSmallInteger('market_id');
            $table->unsignedSmallInteger('bookmaker_id');
            $table->decimal('odd_value', 8, 3);
            $table->decimal('true_probability', 5, 4);
            $table->decimal('api_probability', 5, 4);
            $table->boolean('ev_positive');
            $table->boolean('is_hot_bet');
            $table->boolean('is_trending_bet');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('odds');
    }
};