<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('odds', function (Blueprint $table) {
            $table->string('option_name')->after('bookmaker_id')->default('');
        });
    }

    public function down(): void
    {
        Schema::table('odds', function (Blueprint $table) {
            $table->dropColumn('option_name');
        });
    }
};