<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('odds', function (Blueprint $table) {
            $table->decimal('expected_value', 8, 4)->after('api_probability')->default(0);
        });
    }

    public function down(): void
    {
        Schema::table('odds', function (Blueprint $table) {
            $table->dropColumn('expected_value');
        });
    }
};