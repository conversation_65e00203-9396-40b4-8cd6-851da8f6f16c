# Integração API Betano

## Visão Geral

Esta integração permite obter odds (cuotas) de futebol da Betano através do seu feed público `OddsComparisonFeed`. A implementação inclui cache, rate limiting e formatação padronizada dos dados.

## Configuração

### Variáveis de Ambiente

Adicione ao seu `.env`:

```env
# Betano API Configuration
BETANO_DOMAIN=pt.betano.com
BETANO_LANGUAGE=pt
BETANO_DAYS_AHEAD=7
BETANO_MAX_EVENTS=100
```

### Domínios Disponíveis

- `pt.betano.com` - Portugal
- `br.betano.com` - Brasil
- `es.betano.com` - Espanha
- `de.betano.com` - Alemanha
- `ro.betano.com` - Roménia
- `el.betano.com` - Grécia

### Idiomas Suportados

- `pt` - Português
- `en` - Inglês
- `es` - <PERSON>spanhol
- `de` - <PERSON><PERSON><PERSON>
- `ro` - <PERSON><PERSON>
- `el` - Grego

## Endpoints da API

### 1. Obter <PERSON> as Odds de Futebol

```http
GET /v3/betano-odds
```

**Resposta:**
```json
{
  "success": true,
  "data": [
    {
      "bookmaker_id": 1001,
      "bookmaker_name": "Betano",
      "event_id": "12345",
      "home_team": "Manchester City",
      "away_team": "Real Madrid",
      "league": "Champions League",
      "start_time": "2024-01-15T20:00:00Z",
      "markets": [
        {
          "market_id": 1,
          "market_name": "1X2",
          "outcomes": [
            {
              "outcome_id": "1",
              "outcome_name": "Home",
              "odd": 2.10
            },
            {
              "outcome_id": "X",
              "outcome_name": "Draw",
              "odd": 3.40
            },
            {
              "outcome_id": "2",
              "outcome_name": "Away",
              "odd": 3.20
            }
          ]
        }
      ]
    }
  ],
  "count": 1,
  "source": "Betano",
  "timestamp": "2024-01-15T10:00:00Z"
}
```

### 2. Obter Odds de um Evento Específico

```http
GET /v3/betano-odds/event/{eventId}
```

### 3. Obter Odds de uma Liga Específica

```http
GET /v3/betano-odds/league/{leagueId}
```

### 4. Obter Odds de Hoje

```http
GET /v3/betano-odds/today
```

### 5. Obter Odds para Value Bets

```http
GET /v3/betano-odds/value-bets
```

## Comandos Artisan

### Buscar Odds via Linha de Comando

```bash
# Buscar todas as odds
php artisan betano:fetch-odds

# Buscar odds de hoje
php artisan betano:fetch-odds --today

# Buscar odds de uma liga específica
php artisan betano:fetch-odds --league=123

# Buscar odds de um evento específico
php artisan betano:fetch-odds --event=456

# Mostrar dados brutos
php artisan betano:fetch-odds --show-raw

# Limitar número de eventos exibidos
php artisan betano:fetch-odds --limit=5
```

## Mercados Suportados

A integração mapeia automaticamente os seguintes mercados:

| Market ID | Nome | Descrição |
|-----------|------|-----------|
| 1 | 1X2 | Resultado do jogo (Casa/Empate/Fora) |
| 5 | Goals Over/Under 2.5 | Mais/Menos de 2.5 golos |
| 8 | Both Teams to Score | Ambas as equipas marcam |
| 12 | Double Chance | Dupla hipótese |

## Cache

- **Duração**: 5 minutos para todas as chamadas
- **Chave**: Baseada nos parâmetros da consulta
- **Limpeza**: Automática ou manual via `clearCache()`

## Rate Limiting

- **Limite**: 12 requests por 5 minutos por IP
- **Headers**: `X-RateLimit-*` incluídos nas respostas
- **Erro 429**: Retornado quando limite excedido

## Integração com Value Bets

A integração está preparada para funcionar com o sistema de value bets existente:

```php
// Obter odds formatadas para value bets
$betanoService = app(BetanoFeedService::class);
$odds = $betanoService->getOddsForValueBets();

// Usar no BookmakerOddsService
$bookmakerService = app(BookmakerOddsService::class);
$allOdds = $bookmakerService->getOdds($eventId, 'PT');
```

## Logs

Todos os eventos são registados com prefixo `[BETANO_CLIENT]` ou `[BETANO_FEED_SERVICE]`:

- Sucesso nas chamadas à API
- Erros de rede ou parsing
- Cache hits/misses
- Rate limiting

## Estrutura de Arquivos

```
src/
├── app/
│   ├── Http/
│   │   ├── Clients/Bookmakers/
│   │   │   └── BetanoOddsClient.php
│   │   ├── Controllers/JogosHoje/Api/V3/
│   │   │   └── BetanoOddsController.php
│   │   └── Middleware/
│   │       └── BetanoRateLimitMiddleware.php
│   ├── Services/JogosHoje/
│   │   └── BetanoFeedService.php
│   └── Console/Commands/
│       └── FetchBetanoOddsCommand.php
├── config/
│   └── services.php (configuração Betano)
└── routes/jogoshoje.com/
    └── routes.php (rotas da API)
```

## Troubleshooting

### Erro de Conectividade

1. Verificar se o domínio está correto no `.env`
2. Confirmar que o servidor permite conexões HTTPS de saída
3. Verificar logs para detalhes do erro

### Dados Vazios

1. Verificar se há eventos para a data/liga solicitada
2. Confirmar parâmetros `daysAhead` e `max`
3. Testar com diferentes domínios regionais

### Rate Limiting

1. Reduzir frequência das chamadas
2. Implementar cache mais longo se necessário
3. Usar filtros para reduzir volume de dados

## Próximos Passos

1. **Tracking de Afiliados**: Implementar URLs de afiliado quando necessário
2. **Mais Mercados**: Expandir mapeamento para mercados especiais
3. **Bet365 Integration**: Adicionar suporte similar para Bet365
4. **Webhooks**: Implementar notificações de mudanças de odds
