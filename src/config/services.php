<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'rapidapi' => [
        'host' => env('API_HOST'),
        'key'  => env('API_KEY'),
    ],

    'broadcasters' => [
        'host' => env('BROADCASTS_HOST'),
        'key'  => env('BROADCASTS_KEY'),
    ],

    'predictions' => [
        'host' => env('PREDICTIONS_API_HOST'),
        'key'  => env('PREDICTIONS_API_KEY'),
    ],

    'betano' => [
    'base_url' => env('BETANO_BASE_URL', 'https://pt.betano.com'),
    ],

    'bet365' => [
        'base_url' => env('BET365_BASE_URL', 'https://oddsfeedv2.bet365.com'),
        'username' => env('BET365_USERNAME'),
        'password' => env('BET365_PASSWORD'),
    ],

    'facebook' => [
        'client_id' => env('FACEBOOK_APP_ID'),
        'client_secret' => env('FACEBOOK_APP_SECRET'),
        'redirect' => env('FACEBOOK_CALLBACK')
    ],

    'google' => [
        'client_id' => env('GOOGLE_APP_ID'),
        'client_secret' => env('GOOGLE_APP_SECRET'),
        'redirect' => env('GOOGLE_CALLBACK'),
    ],

    'recaptcha' => [
        'site_key' => env('RECAPTCHA_CLIENT'),
        'secret' => env('RECAPTCHA_SECRET')
    ],

    'thesports' => [
        'host' => env("THESPORTSAPI_HOST"),
        'user' => env("THESPORTSAPI_USER"),
        'secret' => env("THESPORTSAPI_SECRET"),
    ]
];
