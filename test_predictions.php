<?php

// Test script to create sample predictions data
require_once 'vendor/autoload.php';

use App\Models\JogosHoje\FixturePrediction;

// Create a sample prediction for fixture 569488
$prediction = [
    'fixture_id' => 569488,
    '1x2' => [
        'home' => 0.3,
        'draw' => 0.2, 
        'away' => 0.7  // Real Madrid favored
    ],
    'goals' => [
        'btts' => 'Yes',
        'over_2_5' => 'Yes',
        'total_goals' => 3.2
    ],
    'corners' => [
        'total' => 11.5,
        'over_10_5' => 'Yes'
    ],
    'cards' => [
        'total' => 4.2,
        'over_3_5' => 'Yes'
    ]
];

echo "Sample prediction data for fixture 569488:\n";
echo json_encode($prediction, JSON_PRETTY_PRINT);
echo "\n\nThis would create a Super Value bet for Real Madrid wins since:\n";
echo "- Streak shows Real Madrid (4/4 wins) with 90% probability\n";
echo "- Prediction shows Real Madrid with 70% probability (away > 0.5)\n";
echo "- Both support the same outcome: Real Madrid to win\n";
